/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAlim%5CDownloads%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAlim%5CDownloads%5CReal%20Estate%20CRM%20Lite%20Application%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAlim%5CDownloads%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAlim%5CDownloads%5CReal%20Estate%20CRM%20Lite%20Application%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAlim%5CDownloads%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAlim%5CDownloads%5CReal%20Estate%20CRM%20Lite%20Application%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FsaW0lNUMlNUNEb3dubG9hZHMlNUMlNUNSZWFsJTIwRXN0YXRlJTIwQ1JNJTIwTGl0ZSUyMEFwcGxpY2F0aW9uJTIwKDMpJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUFvSCIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWwtZXN0YXRlLWNybS1saXRlLz9iNDI2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWxpbVxcXFxEb3dubG9hZHNcXFxcUmVhbCBFc3RhdGUgQ1JNIExpdGUgQXBwbGljYXRpb24gKDMpXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAlim%5C%5CDownloads%5C%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AppLayout */ \"(ssr)/./components/AppLayout.tsx\");\n/* harmony import */ var _components_screens_Dashboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/screens/Dashboard */ \"(ssr)/./components/screens/Dashboard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleNavigate = (path)=>{\n        router.push(path);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_2__.AppLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_screens_Dashboard__WEBPACK_IMPORTED_MODULE_3__.Dashboard, {\n            onNavigate: handleNavigate\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\app\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\app\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0QztBQUNRO0FBQ1E7QUFFN0MsU0FBU0c7SUFDdEIsTUFBTUMsU0FBU0osMERBQVNBO0lBRXhCLE1BQU1LLGlCQUFpQixDQUFDQztRQUN0QkYsT0FBT0csSUFBSSxDQUFDRDtJQUNkO0lBRUEscUJBQ0UsOERBQUNMLDREQUFTQTtrQkFDUiw0RUFBQ0Msb0VBQVNBO1lBQUNNLFlBQVlIOzs7Ozs7Ozs7OztBQUc3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWwtZXN0YXRlLWNybS1saXRlLy4vYXBwL3BhZ2UudHN4Pzc2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBBcHBMYXlvdXQgfSBmcm9tICcuLi9jb21wb25lbnRzL0FwcExheW91dCc7XG5pbXBvcnQgeyBEYXNoYm9hcmQgfSBmcm9tICcuLi9jb21wb25lbnRzL3NjcmVlbnMvRGFzaGJvYXJkJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkUGFnZSgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgY29uc3QgaGFuZGxlTmF2aWdhdGUgPSAocGF0aDogc3RyaW5nKSA9PiB7XG4gICAgcm91dGVyLnB1c2gocGF0aCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8QXBwTGF5b3V0PlxuICAgICAgPERhc2hib2FyZCBvbk5hdmlnYXRlPXtoYW5kbGVOYXZpZ2F0ZX0gLz5cbiAgICA8L0FwcExheW91dD5cbiAgKTtcbn0iXSwibmFtZXMiOlsidXNlUm91dGVyIiwiQXBwTGF5b3V0IiwiRGFzaGJvYXJkIiwiRGFzaGJvYXJkUGFnZSIsInJvdXRlciIsImhhbmRsZU5hdmlnYXRlIiwicGF0aCIsInB1c2giLCJvbk5hdmlnYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/AppLayout.tsx":
/*!**********************************!*\
  !*** ./components/AppLayout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppLayout: () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _NavigationSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NavigationSidebar */ \"(ssr)/./components/NavigationSidebar.tsx\");\n/* harmony import */ var _NotificationPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationPanel */ \"(ssr)/./components/NotificationPanel.tsx\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../data/mockData */ \"(ssr)/./data/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ AppLayout auto */ \n\n\n\n\n\nfunction AppLayout({ children }) {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_mockData__WEBPACK_IMPORTED_MODULE_5__.mockNotifications);\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleDismissNotification = (notificationId)=>{\n        setNotifications(notifications.filter((n)=>n.id !== notificationId));\n    };\n    const handleNotificationAction = (notificationId, action)=>{\n        if (action === \"view\") {\n            const notification = notifications.find((n)=>n.id === notificationId);\n            if (notification) {\n                // Navigate based on notification type\n                if (notification.title === \"New Lead\") {\n                    router.push(\"/leads\");\n                } else if (notification.title === \"Open House Reminder\") {\n                    router.push(\"/open-houses\");\n                }\n            }\n        }\n        handleDismissNotification(notificationId);\n    };\n    const unreadNotificationCount = notifications.filter((n)=>!n.isRead).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NavigationSidebar__WEBPACK_IMPORTED_MODULE_3__.NavigationSidebar, {\n                collapsed: sidebarCollapsed,\n                onToggle: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                unreadCount: unreadNotificationCount\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationPanel__WEBPACK_IMPORTED_MODULE_4__.NotificationPanel, {\n                notifications: notifications.filter((n)=>!n.isRead),\n                maxVisible: 3,\n                autoHide: false,\n                onDismiss: handleDismissNotification,\n                onAction: handleNotificationAction,\n                onViewAll: ()=>router.push(\"/settings\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\AppLayout.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/AppLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/MetricCard.tsx":
/*!***********************************!*\
  !*** ./components/MetricCard.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MetricCard: () => (/* binding */ MetricCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n\n\n\nfunction MetricCard({ title, value, change, trend, icon, onClick, onDrillDown }) {\n    const formatValue = (val)=>{\n        if (typeof val === \"number\") {\n            if (val >= 1000000) {\n                return `$${(val / 1000000).toFixed(1)}M`;\n            } else if (val >= 1000) {\n                return `${(val / 1000).toFixed(0)}K`;\n            }\n            return val.toLocaleString();\n        }\n        return val;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: `${onClick ? \"cursor-pointer hover:shadow-md transition-shadow\" : \"\"}`,\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-medium\",\n                                    children: formatValue(value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                change !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        trend === \"up\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 36\n                                        }, this),\n                                        trend === \"down\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 38\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-sm ${trend === \"up\" ? \"text-green-600\" : trend === \"down\" ? \"text-red-600\" : \"text-muted-foreground\"}`,\n                                            children: [\n                                                change > 0 ? \"+\" : \"\",\n                                                change,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-muted-foreground\",\n                            children: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                onDrillDown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"text-sm text-primary hover:underline mt-2\",\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        onDrillDown();\n                    },\n                    children: \"View details →\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\MetricCard.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/MetricCard.tsx\n");

/***/ }),

/***/ "(ssr)/./components/NavigationSidebar.tsx":
/*!******************************************!*\
  !*** ./components/NavigationSidebar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationSidebar: () => (/* binding */ NavigationSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,FolderOpen,Home,MessageSquare,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,FolderOpen,Home,MessageSquare,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,FolderOpen,Home,MessageSquare,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,FolderOpen,Home,MessageSquare,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,FolderOpen,Home,MessageSquare,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,FolderOpen,Home,MessageSquare,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,FolderOpen,Home,MessageSquare,Settings,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/avatar */ \"(ssr)/./components/ui/avatar.tsx\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../data/mockData */ \"(ssr)/./data/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ NavigationSidebar auto */ \n\n\n\n\n\n\n\nfunction NavigationSidebar({ collapsed = false, onToggle, onLogout, unreadCount = 0 }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const navItems = [\n        {\n            id: \"dashboard\",\n            label: \"Dashboard\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 39,\n                columnNumber: 13\n            }, this),\n            href: \"/\"\n        },\n        {\n            id: \"leads\",\n            label: \"Leads\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 45,\n                columnNumber: 13\n            }, this),\n            badge: 3,\n            href: \"/leads\"\n        },\n        {\n            id: \"open-houses\",\n            label: \"Open Houses\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, this),\n            href: \"/open-houses\"\n        },\n        {\n            id: \"communications\",\n            label: \"Communications\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 58,\n                columnNumber: 13\n            }, this),\n            badge: unreadCount > 0 ? unreadCount : undefined,\n            href: \"/communications\"\n        },\n        {\n            id: \"documents\",\n            label: \"Documents\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 65,\n                columnNumber: 13\n            }, this),\n            href: \"/documents\"\n        },\n        {\n            id: \"analytics\",\n            label: \"Analytics\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, this),\n            href: \"/analytics\"\n        },\n        {\n            id: \"settings\",\n            label: \"Settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 77,\n                columnNumber: 13\n            }, this),\n            href: \"/settings\"\n        }\n    ];\n    const isActive = (item)=>{\n        if (item.href === \"/\") {\n            return pathname === \"/\";\n        }\n        return pathname.startsWith(item.href);\n    };\n    const NavButton = ({ item })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: item.href,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: isActive(item) ? \"default\" : \"ghost\",\n                className: `w-full justify-start gap-3 ${collapsed ? \"px-2\" : \"px-3\"} ${isActive(item) ? \"bg-sidebar-primary text-sidebar-primary-foreground\" : \"text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\"}`,\n                children: [\n                    item.icon,\n                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1 text-left\",\n                                children: item.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this),\n                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"secondary\",\n                                className: \"ml-auto\",\n                                children: item.badge\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, item.id, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-sidebar border-r border-sidebar-border h-full flex flex-col transition-all duration-300 ${collapsed ? \"w-16\" : \"w-64\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-sidebar-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_FolderOpen_Home_MessageSquare_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 text-primary-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"font-semibold text-sidebar-foreground\",\n                                    children: \"CRM Lite\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-sidebar-foreground/60\",\n                                    children: \"Real Estate\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-2 space-y-1\",\n                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavButton, {\n                        item: item\n                    }, item.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-sidebar-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center gap-3 ${collapsed ? \"justify-center\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                        src: _data_mockData__WEBPACK_IMPORTED_MODULE_6__.mockUser.avatar,\n                                        alt: _data_mockData__WEBPACK_IMPORTED_MODULE_6__.mockUser.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                        children: _data_mockData__WEBPACK_IMPORTED_MODULE_6__.mockUser.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-sidebar-foreground truncate\",\n                                        children: _data_mockData__WEBPACK_IMPORTED_MODULE_6__.mockUser.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-sidebar-foreground/60 truncate\",\n                                        children: _data_mockData__WEBPACK_IMPORTED_MODULE_6__.mockUser.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    !collapsed && onLogout && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        className: \"w-full mt-2 text-sidebar-foreground hover:bg-sidebar-accent\",\n                        onClick: onLogout,\n                        children: \"Sign out\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NavigationSidebar.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/NavigationSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/NotificationPanel.tsx":
/*!******************************************!*\
  !*** ./components/NotificationPanel.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationPanel: () => (/* binding */ NotificationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,Check,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,Check,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,Check,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,Check,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,Check,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,Check,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./components/ui/card.tsx\");\n\n\n\n\n\nfunction NotificationPanel({ notifications, maxVisible = 5, autoHide = false, onDismiss, onAction, onViewAll }) {\n    const [visibleNotifications, setVisibleNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(notifications.slice(0, maxVisible));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setVisibleNotifications(notifications.slice(0, maxVisible));\n    }, [\n        notifications,\n        maxVisible\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoHide) {\n            const timers = visibleNotifications.map((notification)=>setTimeout(()=>{\n                    onDismiss(notification.id);\n                }, 5000));\n            return ()=>{\n                timers.forEach((timer)=>clearTimeout(timer));\n            };\n        }\n    }, [\n        visibleNotifications,\n        autoHide,\n        onDismiss\n    ]);\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getNotificationBorderColor = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"border-l-green-500\";\n            case \"warning\":\n                return \"border-l-yellow-500\";\n            case \"error\":\n                return \"border-l-red-500\";\n            default:\n                return \"border-l-blue-500\";\n        }\n    };\n    if (visibleNotifications.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2 w-80\",\n        children: [\n            visibleNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: `border-l-4 ${getNotificationBorderColor(notification.type)} shadow-md`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                getNotificationIcon(notification.type),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-sm\",\n                                            children: notification.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: notification.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground mt-2\",\n                                            children: new Date(notification.timestamp).toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        onAction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                onClick: ()=>onAction(notification.id, \"view\"),\n                                                children: \"View\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"h-6 w-6 p-0\",\n                                    onClick: ()=>onDismiss(notification.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, notification.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)),\n            notifications.length > maxVisible && onViewAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                variant: \"outline\",\n                size: \"sm\",\n                className: \"w-full\",\n                onClick: onViewAll,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_Check_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    \"View all \",\n                    notifications.length,\n                    \" notifications\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\NotificationPanel.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/NotificationPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./components/screens/Dashboard.tsx":
/*!******************************************!*\
  !*** ./components/screens/Dashboard.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckSquare,DollarSign,MessageSquare,Plus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckSquare,DollarSign,MessageSquare,Plus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckSquare,DollarSign,MessageSquare,Plus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckSquare,DollarSign,MessageSquare,Plus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckSquare,DollarSign,MessageSquare,Plus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckSquare,DollarSign,MessageSquare,Plus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _MetricCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../MetricCard */ \"(ssr)/./components/MetricCard.tsx\");\n/* harmony import */ var _data_mockData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../data/mockData */ \"(ssr)/./data/mockData.ts\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \n\n\n\n\n\nfunction Dashboard({ onNavigate }) {\n    const formatActivityTime = (timestamp)=>{\n        const date = new Date(timestamp);\n        const now = new Date();\n        const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 1) {\n            return \"Just now\";\n        } else if (diffInHours < 24) {\n            return `${Math.floor(diffInHours)}h ago`;\n        } else {\n            return `${Math.floor(diffInHours / 24)}d ago`;\n        }\n    };\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"lead-added\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 16\n                }, this);\n            case \"open-house-scheduled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, this);\n            case \"message-sent\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-purple-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 16\n                }, this);\n            case \"document-uploaded\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-orange-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const upcomingAppointments = _data_mockData__WEBPACK_IMPORTED_MODULE_4__.mockOpenHouses.filter((oh)=>new Date(oh.date) >= new Date()).slice(0, 3);\n    const pendingTasks = [\n        {\n            id: \"1\",\n            title: \"Follow up with John Smith\",\n            dueDate: \"2025-01-27\"\n        },\n        {\n            id: \"2\",\n            title: \"Prepare Downtown Condo materials\",\n            dueDate: \"2025-01-28\"\n        },\n        {\n            id: \"3\",\n            title: \"Schedule property viewing for Emily Davis\",\n            dueDate: \"2025-01-29\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Good morning, Sarah!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Here's what's happening with your business today.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: ()=>onNavigate(\"/leads\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Lead\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>onNavigate(\"/open-houses\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Schedule Open House\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetricCard__WEBPACK_IMPORTED_MODULE_3__.MetricCard, {\n                        title: \"Total Leads\",\n                        value: _data_mockData__WEBPACK_IMPORTED_MODULE_4__.mockMetrics.totalLeads,\n                        change: 12,\n                        trend: \"up\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 17\n                        }, void 0),\n                        onClick: ()=>onNavigate(\"/leads\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetricCard__WEBPACK_IMPORTED_MODULE_3__.MetricCard, {\n                        title: \"Open Houses This Week\",\n                        value: _data_mockData__WEBPACK_IMPORTED_MODULE_4__.mockMetrics.openHousesThisWeek,\n                        change: 25,\n                        trend: \"up\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 17\n                        }, void 0),\n                        onClick: ()=>onNavigate(\"/open-houses\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetricCard__WEBPACK_IMPORTED_MODULE_3__.MetricCard, {\n                        title: \"Pending Tasks\",\n                        value: _data_mockData__WEBPACK_IMPORTED_MODULE_4__.mockMetrics.pendingTasks,\n                        change: -8,\n                        trend: \"down\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MetricCard__WEBPACK_IMPORTED_MODULE_3__.MetricCard, {\n                        title: \"Revenue Pipeline\",\n                        value: _data_mockData__WEBPACK_IMPORTED_MODULE_4__.mockMetrics.revenuePipeline,\n                        change: 18,\n                        trend: \"up\",\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 17\n                        }, void 0),\n                        onClick: ()=>onNavigate(\"/analytics\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        _data_mockData__WEBPACK_IMPORTED_MODULE_4__.mockActivities.slice(0, 5).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1\",\n                                                        children: getActivityIcon(activity.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: activity.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: formatActivityTime(activity.timestamp)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"w-full\",\n                                            onClick: ()=>onNavigate(\"/analytics\"),\n                                            children: \"View all activity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Upcoming Appointments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        upcomingAppointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 p-3 bg-muted/50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: appointment.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    new Date(appointment.date).toLocaleDateString(),\n                                                                    \" at \",\n                                                                    appointment.startTime\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: appointment.propertyAddress\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, appointment.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"w-full\",\n                                            onClick: ()=>onNavigate(\"/open-houses\"),\n                                            children: \"View calendar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Pending Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        pendingTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        className: \"rounded border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: task.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Due: \",\n                                                                    new Date(task.dueDate).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, task.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"w-full\",\n                                            onClick: ()=>onNavigate(\"/leads\"),\n                                            children: \"View all tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"h-20 flex-col gap-2\",\n                                    onClick: ()=>onNavigate(\"/leads\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Add New Lead\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"h-20 flex-col gap-2\",\n                                    onClick: ()=>onNavigate(\"/open-houses\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Schedule Open House\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"h-20 flex-col gap-2\",\n                                    onClick: ()=>onNavigate(\"/communications\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckSquare_DollarSign_MessageSquare_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Send Message\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\screens\\\\Dashboard.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/screens/Dashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./components/ui/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarImage,AvatarFallback auto */ \n\n\n\nfunction Avatar({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"avatar\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex size-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction AvatarImage({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        \"data-slot\": \"avatar-image\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square size-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction AvatarFallback({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        \"data-slot\": \"avatar-fallback\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-muted flex size-full items-center justify-center rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./components/ui/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2JhZGdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQStCO0FBQ2E7QUFDc0I7QUFFckM7QUFFN0IsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkIsa1pBQ0E7SUFDRUcsVUFBVTtRQUNSQyxTQUFTO1lBQ1BDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7UUFDSjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmTCxTQUFTO0lBQ1g7QUFDRjtBQUdGLFNBQVNNLE1BQU0sRUFDYkMsU0FBUyxFQUNUUCxPQUFPLEVBQ1BRLFVBQVUsS0FBSyxFQUNmLEdBQUdDLE9BRXVEO0lBQzFELE1BQU1DLE9BQU9GLFVBQVViLHNEQUFJQSxHQUFHO0lBRTlCLHFCQUNFLDhEQUFDZTtRQUNDQyxhQUFVO1FBQ1ZKLFdBQVdWLDBDQUFFQSxDQUFDQyxjQUFjO1lBQUVFO1FBQVEsSUFBSU87UUFDekMsR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7QUFFZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsLWVzdGF0ZS1jcm0tbGl0ZS8uL2NvbXBvbmVudHMvdWkvYmFkZ2UudHN4PzdjZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCI7XG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiO1xuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCIuL3V0aWxzXCI7XG5cbmNvbnN0IGJhZGdlVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHJvdW5kZWQtbWQgYm9yZGVyIHB4LTIgcHktMC41IHRleHQteHMgZm9udC1tZWRpdW0gdy1maXQgd2hpdGVzcGFjZS1ub3dyYXAgc2hyaW5rLTAgWyY+c3ZnXTpzaXplLTMgZ2FwLTEgWyY+c3ZnXTpwb2ludGVyLWV2ZW50cy1ub25lIGZvY3VzLXZpc2libGU6Ym9yZGVyLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcvNTAgZm9jdXMtdmlzaWJsZTpyaW5nLVszcHhdIGFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzIwIGRhcms6YXJpYS1pbnZhbGlkOnJpbmctZGVzdHJ1Y3RpdmUvNDAgYXJpYS1pbnZhbGlkOmJvcmRlci1kZXN0cnVjdGl2ZSB0cmFuc2l0aW9uLVtjb2xvcixib3gtc2hhZG93XSBvdmVyZmxvdy1oaWRkZW5cIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgXCJib3JkZXItdHJhbnNwYXJlbnQgYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBbYSZdOmhvdmVyOmJnLXByaW1hcnkvOTBcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLXNlY29uZGFyeSB0ZXh0LXNlY29uZGFyeS1mb3JlZ3JvdW5kIFthJl06aG92ZXI6Ymctc2Vjb25kYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYm9yZGVyLXRyYW5zcGFyZW50IGJnLWRlc3RydWN0aXZlIHRleHQtd2hpdGUgW2EmXTpob3ZlcjpiZy1kZXN0cnVjdGl2ZS85MCBmb2N1cy12aXNpYmxlOnJpbmctZGVzdHJ1Y3RpdmUvMjAgZGFyazpmb2N1cy12aXNpYmxlOnJpbmctZGVzdHJ1Y3RpdmUvNDAgZGFyazpiZy1kZXN0cnVjdGl2ZS82MFwiLFxuICAgICAgICBvdXRsaW5lOlxuICAgICAgICAgIFwidGV4dC1mb3JlZ3JvdW5kIFthJl06aG92ZXI6YmctYWNjZW50IFthJl06aG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgfSxcbiAgfSxcbik7XG5cbmZ1bmN0aW9uIEJhZGdlKHtcbiAgY2xhc3NOYW1lLFxuICB2YXJpYW50LFxuICBhc0NoaWxkID0gZmFsc2UsXG4gIC4uLnByb3BzXG59OiBSZWFjdC5Db21wb25lbnRQcm9wczxcInNwYW5cIj4gJlxuICBWYXJpYW50UHJvcHM8dHlwZW9mIGJhZGdlVmFyaWFudHM+ICYgeyBhc0NoaWxkPzogYm9vbGVhbiB9KSB7XG4gIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwic3BhblwiO1xuXG4gIHJldHVybiAoXG4gICAgPENvbXBcbiAgICAgIGRhdGEtc2xvdD1cImJhZGdlXCJcbiAgICAgIGNsYXNzTmFtZT17Y24oYmFkZ2VWYXJpYW50cyh7IHZhcmlhbnQgfSksIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn1cblxuZXhwb3J0IHsgQmFkZ2UsIGJhZGdlVmFyaWFudHMgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJhZGdlVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0Iiwic2Vjb25kYXJ5IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwiZGVmYXVsdFZhcmlhbnRzIiwiQmFkZ2UiLCJjbGFzc05hbWUiLCJhc0NoaWxkIiwicHJvcHMiLCJDb21wIiwiZGF0YS1zbG90Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./components/ui/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background text-foreground hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9 rounded-md\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./components/ui/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6 [&:last-child]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 pb-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/utils.ts":
/*!********************************!*\
  !*** ./components/ui/utils.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUNKO0FBRWxDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWwtZXN0YXRlLWNybS1saXRlLy4vY29tcG9uZW50cy91aS91dGlscy50cz82OGE1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCI7XG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpO1xufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/utils.ts\n");

/***/ }),

/***/ "(ssr)/./data/mockData.ts":
/*!**************************!*\
  !*** ./data/mockData.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockActivities: () => (/* binding */ mockActivities),\n/* harmony export */   mockDocuments: () => (/* binding */ mockDocuments),\n/* harmony export */   mockFolders: () => (/* binding */ mockFolders),\n/* harmony export */   mockLeads: () => (/* binding */ mockLeads),\n/* harmony export */   mockMessages: () => (/* binding */ mockMessages),\n/* harmony export */   mockMetrics: () => (/* binding */ mockMetrics),\n/* harmony export */   mockNotifications: () => (/* binding */ mockNotifications),\n/* harmony export */   mockOpenHouses: () => (/* binding */ mockOpenHouses),\n/* harmony export */   mockUser: () => (/* binding */ mockUser)\n/* harmony export */ });\nconst mockLeads = [\n    {\n        id: \"1\",\n        name: \"John Smith\",\n        email: \"<EMAIL>\",\n        phone: \"(*************\",\n        status: \"qualified\",\n        source: \"Website\",\n        propertyInterest: \"3BR Condo Downtown\",\n        budget: 450000,\n        lastContact: \"2025-01-25\",\n        nextAction: \"Schedule property viewing\",\n        assignedAgent: \"Sarah Johnson\",\n        createdAt: \"2025-01-20\",\n        notes: \"Looking for move-in ready property\"\n    },\n    {\n        id: \"2\",\n        name: \"Emily Davis\",\n        email: \"<EMAIL>\",\n        phone: \"(*************\",\n        status: \"new\",\n        source: \"Referral\",\n        propertyInterest: \"4BR House Suburbs\",\n        budget: 650000,\n        lastContact: \"2025-01-26\",\n        nextAction: \"Initial consultation call\",\n        assignedAgent: \"Mike Wilson\",\n        createdAt: \"2025-01-26\",\n        notes: \"First-time home buyer\"\n    },\n    {\n        id: \"3\",\n        name: \"Robert Johnson\",\n        email: \"<EMAIL>\",\n        phone: \"(*************\",\n        status: \"contacted\",\n        source: \"Facebook Ad\",\n        propertyInterest: \"2BR Apartment\",\n        budget: 300000,\n        lastContact: \"2025-01-24\",\n        nextAction: \"Send property listings\",\n        assignedAgent: \"Sarah Johnson\",\n        createdAt: \"2025-01-22\",\n        notes: \"Interested in modern amenities\"\n    }\n];\nconst mockOpenHouses = [\n    {\n        id: \"1\",\n        title: \"Modern Downtown Condo\",\n        propertyAddress: \"123 Main St, Downtown\",\n        date: \"2025-01-30\",\n        startTime: \"14:00\",\n        endTime: \"16:00\",\n        agent: \"Sarah Johnson\",\n        attendees: 12,\n        status: \"scheduled\",\n        description: \"Luxury 3BR condo with city views\"\n    },\n    {\n        id: \"2\",\n        title: \"Family Home in Suburbs\",\n        propertyAddress: \"456 Oak Ave, Suburbs\",\n        date: \"2025-02-01\",\n        startTime: \"10:00\",\n        endTime: \"12:00\",\n        agent: \"Mike Wilson\",\n        attendees: 8,\n        status: \"scheduled\",\n        description: \"4BR house with large backyard\"\n    },\n    {\n        id: \"3\",\n        title: \"Starter Home\",\n        propertyAddress: \"789 Pine St, Eastside\",\n        date: \"2025-01-28\",\n        startTime: \"13:00\",\n        endTime: \"15:00\",\n        agent: \"Sarah Johnson\",\n        attendees: 15,\n        status: \"completed\",\n        description: \"2BR house perfect for first-time buyers\"\n    }\n];\nconst mockMessages = [\n    {\n        id: \"1\",\n        senderId: \"agent1\",\n        recipientId: \"1\",\n        content: \"Hi John, I found some properties that match your criteria. Would you like to schedule a viewing?\",\n        timestamp: \"2025-01-26T10:30:00Z\",\n        type: \"text\",\n        isRead: true\n    },\n    {\n        id: \"2\",\n        senderId: \"1\",\n        recipientId: \"agent1\",\n        content: \"Yes, that sounds great! I'm available this weekend.\",\n        timestamp: \"2025-01-26T11:15:00Z\",\n        type: \"text\",\n        isRead: true\n    },\n    {\n        id: \"3\",\n        senderId: \"agent1\",\n        recipientId: \"2\",\n        content: \"Welcome to our service! I'll be your agent. Let's set up a consultation call.\",\n        timestamp: \"2025-01-26T09:00:00Z\",\n        type: \"text\",\n        isRead: false\n    }\n];\nconst mockDocuments = [\n    {\n        id: \"1\",\n        name: \"Property_Agreement_John_Smith.pdf\",\n        type: \"pdf\",\n        size: 245760,\n        uploadedAt: \"2025-01-25T14:20:00Z\",\n        leadId: \"1\",\n        url: \"#\"\n    },\n    {\n        id: \"2\",\n        name: \"Property_Photos_Downtown_Condo.zip\",\n        type: \"zip\",\n        size: 15728640,\n        uploadedAt: \"2025-01-24T16:45:00Z\",\n        url: \"#\"\n    },\n    {\n        id: \"3\",\n        name: \"Market_Analysis_Q1_2025.xlsx\",\n        type: \"xlsx\",\n        size: 524288,\n        uploadedAt: \"2025-01-26T11:30:00Z\",\n        folderId: \"folder1\",\n        url: \"#\"\n    }\n];\nconst mockFolders = [\n    {\n        id: \"folder1\",\n        name: \"Market Reports\",\n        createdAt: \"2025-01-20T10:00:00Z\"\n    },\n    {\n        id: \"folder2\",\n        name: \"Client Documents\",\n        createdAt: \"2025-01-22T14:30:00Z\"\n    },\n    {\n        id: \"folder3\",\n        name: \"Property Photos\",\n        createdAt: \"2025-01-18T09:15:00Z\"\n    }\n];\nconst mockUser = {\n    id: \"user1\",\n    name: \"Sarah Johnson\",\n    email: \"<EMAIL>\",\n    role: \"agent\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b55c?w=150&h=150&fit=crop&crop=face\"\n};\nconst mockNotifications = [\n    {\n        id: \"1\",\n        title: \"New Lead\",\n        message: \"Emily Davis submitted an inquiry\",\n        type: \"info\",\n        timestamp: \"2025-01-26T12:00:00Z\",\n        isRead: false\n    },\n    {\n        id: \"2\",\n        title: \"Open House Reminder\",\n        message: \"Downtown Condo open house starts in 2 hours\",\n        type: \"warning\",\n        timestamp: \"2025-01-26T12:00:00Z\",\n        isRead: false\n    },\n    {\n        id: \"3\",\n        title: \"Document Uploaded\",\n        message: \"Property agreement signed by John Smith\",\n        type: \"success\",\n        timestamp: \"2025-01-25T14:20:00Z\",\n        isRead: true\n    }\n];\nconst mockActivities = [\n    {\n        id: \"1\",\n        type: \"lead-added\",\n        description: \"New lead Emily Davis added from referral\",\n        timestamp: \"2025-01-26T12:00:00Z\",\n        userId: \"user1\",\n        relatedId: \"2\"\n    },\n    {\n        id: \"2\",\n        type: \"open-house-scheduled\",\n        description: \"Open house scheduled for Downtown Condo\",\n        timestamp: \"2025-01-25T15:30:00Z\",\n        userId: \"user1\",\n        relatedId: \"1\"\n    },\n    {\n        id: \"3\",\n        type: \"message-sent\",\n        description: \"Message sent to John Smith about property viewing\",\n        timestamp: \"2025-01-26T10:30:00Z\",\n        userId: \"user1\",\n        relatedId: \"1\"\n    },\n    {\n        id: \"4\",\n        type: \"document-uploaded\",\n        description: \"Property agreement uploaded for John Smith\",\n        timestamp: \"2025-01-25T14:20:00Z\",\n        userId: \"user1\",\n        relatedId: \"1\"\n    }\n];\nconst mockMetrics = {\n    totalLeads: 147,\n    openHousesThisWeek: 5,\n    pendingTasks: 12,\n    revenuePipeline: 2450000\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./data/mockData.ts\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"046cb806435c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFsLWVzdGF0ZS1jcm0tbGl0ZS8uL3N0eWxlcy9nbG9iYWxzLmNzcz8yN2YxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDQ2Y2I4MDY0MzVjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Real Estate CRM Lite\",\n    description: \"A streamlined CRM platform for real estate professionals\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Real Estate CRM Lite Application (3)\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZ3QjtBQUl2QixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwySkFBZTtzQkFDN0JLOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhbC1lc3RhdGUtY3JtLWxpdGUvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdSZWFsIEVzdGF0ZSBDUk0gTGl0ZScsXG4gIGRlc2NyaXB0aW9uOiAnQSBzdHJlYW1saW5lZCBDUk0gcGxhdGZvcm0gZm9yIHJlYWwgZXN0YXRlIHByb2Zlc3Npb25hbHMnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufSJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\Real Estate CRM Lite Application (3)\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/@swc","vendor-chunks/use-sync-external-store","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CAlim%5CDownloads%5CReal%20Estate%20CRM%20Lite%20Application%20(3)%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAlim%5CDownloads%5CReal%20Estate%20CRM%20Lite%20Application%20(3)&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();