'use client'

import { AppLayout } from '../../../components/AppLayout';
import { LeadDetail } from '../../../components/screens/LeadDetail';

interface LeadDetailPageProps {
  params: {
    id: string;
  };
}

export default function LeadDetailPage({ params }: LeadDetailPageProps) {
  const handleNavigate = (path: string) => {
    window.location.href = path;
  };

  return (
    <AppLayout>
      <LeadDetail leadId={params.id} onNavigate={handleNavigate} />
    </AppLayout>
  );
}