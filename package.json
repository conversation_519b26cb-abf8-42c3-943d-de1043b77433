{"name": "real-estate-crm-lite", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toggle": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.487.0", "recharts": "^2.15.2", "react-hook-form": "^7.55.0", "embla-carousel-react": "^8.6.0", "next-themes": "^0.4.6", "sonner": "^2.0.3"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/d3-color": "^3.1.0", "@types/d3-scale": "^4.0.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "postcss": "^8.4.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}