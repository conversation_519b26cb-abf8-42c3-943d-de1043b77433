'use client'

import { useRouter } from 'next/navigation';
import { AppLayout } from '../../components/AppLayout';
import { Documents } from '../../components/screens/Documents';

export default function DocumentsPage() {
  const router = useRouter();

  const handleNavigate = (path: string) => {
    router.push(path);
  };

  return (
    <AppLayout>
      <Documents onNavigate={handleNavigate} />
    </AppLayout>
  );
}