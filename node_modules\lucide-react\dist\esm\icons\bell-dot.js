/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10.268 21a2 2 0 0 0 3.464 0", key: "vwvbt9" }],
  [
    "path",
    {
      d: "M13.916 2.314A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.74 7.327A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673 9 9 0 0 1-.585-.665",
      key: "1tip0g"
    }
  ],
  ["circle", { cx: "18", cy: "8", r: "3", key: "1g0gzu" }]
];
const BellDot = createLucideIcon("bell-dot", __iconNode);

export { __iconNode, BellDot as default };
//# sourceMappingURL=bell-dot.js.map
