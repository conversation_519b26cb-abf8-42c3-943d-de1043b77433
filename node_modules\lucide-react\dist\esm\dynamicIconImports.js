/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const dynamicIconImports = {
  "a-arrow-down": () => import('./icons/a-arrow-down.js'),
  "a-arrow-up": () => import('./icons/a-arrow-up.js'),
  "a-large-small": () => import('./icons/a-large-small.js'),
  "accessibility": () => import('./icons/accessibility.js'),
  "activity": () => import('./icons/activity.js'),
  "air-vent": () => import('./icons/air-vent.js'),
  "airplay": () => import('./icons/airplay.js'),
  "alarm-clock-check": () => import('./icons/alarm-clock-check.js'),
  "alarm-check": () => import('./icons/alarm-clock-check.js'),
  "alarm-clock-minus": () => import('./icons/alarm-clock-minus.js'),
  "alarm-minus": () => import('./icons/alarm-clock-minus.js'),
  "alarm-clock-off": () => import('./icons/alarm-clock-off.js'),
  "alarm-clock-plus": () => import('./icons/alarm-clock-plus.js'),
  "alarm-plus": () => import('./icons/alarm-clock-plus.js'),
  "alarm-clock": () => import('./icons/alarm-clock.js'),
  "alarm-smoke": () => import('./icons/alarm-smoke.js'),
  "album": () => import('./icons/album.js'),
  "align-center-horizontal": () => import('./icons/align-center-horizontal.js'),
  "align-center-vertical": () => import('./icons/align-center-vertical.js'),
  "align-center": () => import('./icons/align-center.js'),
  "align-end-horizontal": () => import('./icons/align-end-horizontal.js'),
  "align-end-vertical": () => import('./icons/align-end-vertical.js'),
  "align-horizontal-distribute-center": () => import('./icons/align-horizontal-distribute-center.js'),
  "align-horizontal-distribute-end": () => import('./icons/align-horizontal-distribute-end.js'),
  "align-horizontal-distribute-start": () => import('./icons/align-horizontal-distribute-start.js'),
  "align-horizontal-justify-center": () => import('./icons/align-horizontal-justify-center.js'),
  "align-horizontal-justify-end": () => import('./icons/align-horizontal-justify-end.js'),
  "align-horizontal-justify-start": () => import('./icons/align-horizontal-justify-start.js'),
  "align-horizontal-space-around": () => import('./icons/align-horizontal-space-around.js'),
  "align-horizontal-space-between": () => import('./icons/align-horizontal-space-between.js'),
  "align-justify": () => import('./icons/align-justify.js'),
  "align-left": () => import('./icons/align-left.js'),
  "align-right": () => import('./icons/align-right.js'),
  "align-start-horizontal": () => import('./icons/align-start-horizontal.js'),
  "align-start-vertical": () => import('./icons/align-start-vertical.js'),
  "align-vertical-distribute-center": () => import('./icons/align-vertical-distribute-center.js'),
  "align-vertical-distribute-end": () => import('./icons/align-vertical-distribute-end.js'),
  "align-vertical-distribute-start": () => import('./icons/align-vertical-distribute-start.js'),
  "align-vertical-justify-center": () => import('./icons/align-vertical-justify-center.js'),
  "align-vertical-justify-end": () => import('./icons/align-vertical-justify-end.js'),
  "align-vertical-justify-start": () => import('./icons/align-vertical-justify-start.js'),
  "align-vertical-space-around": () => import('./icons/align-vertical-space-around.js'),
  "align-vertical-space-between": () => import('./icons/align-vertical-space-between.js'),
  "ambulance": () => import('./icons/ambulance.js'),
  "ampersand": () => import('./icons/ampersand.js'),
  "ampersands": () => import('./icons/ampersands.js'),
  "amphora": () => import('./icons/amphora.js'),
  "anchor": () => import('./icons/anchor.js'),
  "angry": () => import('./icons/angry.js'),
  "annoyed": () => import('./icons/annoyed.js'),
  "antenna": () => import('./icons/antenna.js'),
  "anvil": () => import('./icons/anvil.js'),
  "aperture": () => import('./icons/aperture.js'),
  "app-window-mac": () => import('./icons/app-window-mac.js'),
  "app-window": () => import('./icons/app-window.js'),
  "apple": () => import('./icons/apple.js'),
  "archive-restore": () => import('./icons/archive-restore.js'),
  "archive-x": () => import('./icons/archive-x.js'),
  "archive": () => import('./icons/archive.js'),
  "armchair": () => import('./icons/armchair.js'),
  "arrow-big-down-dash": () => import('./icons/arrow-big-down-dash.js'),
  "arrow-big-down": () => import('./icons/arrow-big-down.js'),
  "arrow-big-left-dash": () => import('./icons/arrow-big-left-dash.js'),
  "arrow-big-left": () => import('./icons/arrow-big-left.js'),
  "arrow-big-right-dash": () => import('./icons/arrow-big-right-dash.js'),
  "arrow-big-right": () => import('./icons/arrow-big-right.js'),
  "arrow-big-up-dash": () => import('./icons/arrow-big-up-dash.js'),
  "arrow-big-up": () => import('./icons/arrow-big-up.js'),
  "arrow-down-0-1": () => import('./icons/arrow-down-0-1.js'),
  "arrow-down-01": () => import('./icons/arrow-down-0-1.js'),
  "arrow-down-1-0": () => import('./icons/arrow-down-1-0.js'),
  "arrow-down-10": () => import('./icons/arrow-down-1-0.js'),
  "arrow-down-a-z": () => import('./icons/arrow-down-a-z.js'),
  "arrow-down-az": () => import('./icons/arrow-down-a-z.js'),
  "arrow-down-from-line": () => import('./icons/arrow-down-from-line.js'),
  "arrow-down-left": () => import('./icons/arrow-down-left.js'),
  "arrow-down-narrow-wide": () => import('./icons/arrow-down-narrow-wide.js'),
  "arrow-down-right": () => import('./icons/arrow-down-right.js'),
  "arrow-down-to-dot": () => import('./icons/arrow-down-to-dot.js'),
  "arrow-down-to-line": () => import('./icons/arrow-down-to-line.js'),
  "arrow-down-up": () => import('./icons/arrow-down-up.js'),
  "arrow-down-wide-narrow": () => import('./icons/arrow-down-wide-narrow.js'),
  "sort-desc": () => import('./icons/arrow-down-wide-narrow.js'),
  "arrow-down-z-a": () => import('./icons/arrow-down-z-a.js'),
  "arrow-down-za": () => import('./icons/arrow-down-z-a.js'),
  "arrow-down": () => import('./icons/arrow-down.js'),
  "arrow-left-from-line": () => import('./icons/arrow-left-from-line.js'),
  "arrow-left-right": () => import('./icons/arrow-left-right.js'),
  "arrow-left-to-line": () => import('./icons/arrow-left-to-line.js'),
  "arrow-left": () => import('./icons/arrow-left.js'),
  "arrow-right-from-line": () => import('./icons/arrow-right-from-line.js'),
  "arrow-right-left": () => import('./icons/arrow-right-left.js'),
  "arrow-right-to-line": () => import('./icons/arrow-right-to-line.js'),
  "arrow-right": () => import('./icons/arrow-right.js'),
  "arrow-up-0-1": () => import('./icons/arrow-up-0-1.js'),
  "arrow-up-01": () => import('./icons/arrow-up-0-1.js'),
  "arrow-up-1-0": () => import('./icons/arrow-up-1-0.js'),
  "arrow-up-10": () => import('./icons/arrow-up-1-0.js'),
  "arrow-up-a-z": () => import('./icons/arrow-up-a-z.js'),
  "arrow-up-az": () => import('./icons/arrow-up-a-z.js'),
  "arrow-up-down": () => import('./icons/arrow-up-down.js'),
  "arrow-up-from-dot": () => import('./icons/arrow-up-from-dot.js'),
  "arrow-up-from-line": () => import('./icons/arrow-up-from-line.js'),
  "arrow-up-left": () => import('./icons/arrow-up-left.js'),
  "arrow-up-narrow-wide": () => import('./icons/arrow-up-narrow-wide.js'),
  "sort-asc": () => import('./icons/arrow-up-narrow-wide.js'),
  "arrow-up-right": () => import('./icons/arrow-up-right.js'),
  "arrow-up-to-line": () => import('./icons/arrow-up-to-line.js'),
  "arrow-up-wide-narrow": () => import('./icons/arrow-up-wide-narrow.js'),
  "arrow-up-z-a": () => import('./icons/arrow-up-z-a.js'),
  "arrow-up-za": () => import('./icons/arrow-up-z-a.js'),
  "arrow-up": () => import('./icons/arrow-up.js'),
  "arrows-up-from-line": () => import('./icons/arrows-up-from-line.js'),
  "asterisk": () => import('./icons/asterisk.js'),
  "at-sign": () => import('./icons/at-sign.js'),
  "atom": () => import('./icons/atom.js'),
  "audio-lines": () => import('./icons/audio-lines.js'),
  "audio-waveform": () => import('./icons/audio-waveform.js'),
  "award": () => import('./icons/award.js'),
  "axe": () => import('./icons/axe.js'),
  "axis-3d": () => import('./icons/axis-3d.js'),
  "axis-3-d": () => import('./icons/axis-3d.js'),
  "baby": () => import('./icons/baby.js'),
  "backpack": () => import('./icons/backpack.js'),
  "badge-alert": () => import('./icons/badge-alert.js'),
  "badge-cent": () => import('./icons/badge-cent.js'),
  "badge-check": () => import('./icons/badge-check.js'),
  "verified": () => import('./icons/badge-check.js'),
  "badge-dollar-sign": () => import('./icons/badge-dollar-sign.js'),
  "badge-euro": () => import('./icons/badge-euro.js'),
  "badge-help": () => import('./icons/badge-help.js'),
  "badge-indian-rupee": () => import('./icons/badge-indian-rupee.js'),
  "badge-info": () => import('./icons/badge-info.js'),
  "badge-japanese-yen": () => import('./icons/badge-japanese-yen.js'),
  "badge-minus": () => import('./icons/badge-minus.js'),
  "badge-percent": () => import('./icons/badge-percent.js'),
  "badge-plus": () => import('./icons/badge-plus.js'),
  "badge-pound-sterling": () => import('./icons/badge-pound-sterling.js'),
  "badge-russian-ruble": () => import('./icons/badge-russian-ruble.js'),
  "badge-swiss-franc": () => import('./icons/badge-swiss-franc.js'),
  "badge-x": () => import('./icons/badge-x.js'),
  "badge": () => import('./icons/badge.js'),
  "baggage-claim": () => import('./icons/baggage-claim.js'),
  "ban": () => import('./icons/ban.js'),
  "banana": () => import('./icons/banana.js'),
  "bandage": () => import('./icons/bandage.js'),
  "banknote-arrow-down": () => import('./icons/banknote-arrow-down.js'),
  "banknote-arrow-up": () => import('./icons/banknote-arrow-up.js'),
  "banknote-x": () => import('./icons/banknote-x.js'),
  "banknote": () => import('./icons/banknote.js'),
  "barcode": () => import('./icons/barcode.js'),
  "baseline": () => import('./icons/baseline.js'),
  "bath": () => import('./icons/bath.js'),
  "battery-charging": () => import('./icons/battery-charging.js'),
  "battery-full": () => import('./icons/battery-full.js'),
  "battery-low": () => import('./icons/battery-low.js'),
  "battery-medium": () => import('./icons/battery-medium.js'),
  "battery-plus": () => import('./icons/battery-plus.js'),
  "battery-warning": () => import('./icons/battery-warning.js'),
  "battery": () => import('./icons/battery.js'),
  "beaker": () => import('./icons/beaker.js'),
  "bean-off": () => import('./icons/bean-off.js'),
  "bean": () => import('./icons/bean.js'),
  "bed-double": () => import('./icons/bed-double.js'),
  "bed-single": () => import('./icons/bed-single.js'),
  "bed": () => import('./icons/bed.js'),
  "beef": () => import('./icons/beef.js'),
  "beer-off": () => import('./icons/beer-off.js'),
  "beer": () => import('./icons/beer.js'),
  "bell-dot": () => import('./icons/bell-dot.js'),
  "bell-electric": () => import('./icons/bell-electric.js'),
  "bell-minus": () => import('./icons/bell-minus.js'),
  "bell-off": () => import('./icons/bell-off.js'),
  "bell-plus": () => import('./icons/bell-plus.js'),
  "bell-ring": () => import('./icons/bell-ring.js'),
  "bell": () => import('./icons/bell.js'),
  "between-horizontal-end": () => import('./icons/between-horizontal-end.js'),
  "between-horizonal-end": () => import('./icons/between-horizontal-end.js'),
  "between-horizontal-start": () => import('./icons/between-horizontal-start.js'),
  "between-horizonal-start": () => import('./icons/between-horizontal-start.js'),
  "between-vertical-end": () => import('./icons/between-vertical-end.js'),
  "between-vertical-start": () => import('./icons/between-vertical-start.js'),
  "biceps-flexed": () => import('./icons/biceps-flexed.js'),
  "bike": () => import('./icons/bike.js'),
  "binary": () => import('./icons/binary.js'),
  "binoculars": () => import('./icons/binoculars.js'),
  "biohazard": () => import('./icons/biohazard.js'),
  "bird": () => import('./icons/bird.js'),
  "bitcoin": () => import('./icons/bitcoin.js'),
  "blend": () => import('./icons/blend.js'),
  "blinds": () => import('./icons/blinds.js'),
  "blocks": () => import('./icons/blocks.js'),
  "bluetooth-connected": () => import('./icons/bluetooth-connected.js'),
  "bluetooth-off": () => import('./icons/bluetooth-off.js'),
  "bluetooth-searching": () => import('./icons/bluetooth-searching.js'),
  "bluetooth": () => import('./icons/bluetooth.js'),
  "bold": () => import('./icons/bold.js'),
  "bolt": () => import('./icons/bolt.js'),
  "bomb": () => import('./icons/bomb.js'),
  "bone": () => import('./icons/bone.js'),
  "book-a": () => import('./icons/book-a.js'),
  "book-audio": () => import('./icons/book-audio.js'),
  "book-check": () => import('./icons/book-check.js'),
  "book-copy": () => import('./icons/book-copy.js'),
  "book-dashed": () => import('./icons/book-dashed.js'),
  "book-template": () => import('./icons/book-dashed.js'),
  "book-down": () => import('./icons/book-down.js'),
  "book-headphones": () => import('./icons/book-headphones.js'),
  "book-heart": () => import('./icons/book-heart.js'),
  "book-image": () => import('./icons/book-image.js'),
  "book-key": () => import('./icons/book-key.js'),
  "book-lock": () => import('./icons/book-lock.js'),
  "book-marked": () => import('./icons/book-marked.js'),
  "book-minus": () => import('./icons/book-minus.js'),
  "book-open-check": () => import('./icons/book-open-check.js'),
  "book-open-text": () => import('./icons/book-open-text.js'),
  "book-open": () => import('./icons/book-open.js'),
  "book-plus": () => import('./icons/book-plus.js'),
  "book-text": () => import('./icons/book-text.js'),
  "book-type": () => import('./icons/book-type.js'),
  "book-up-2": () => import('./icons/book-up-2.js'),
  "book-up": () => import('./icons/book-up.js'),
  "book-user": () => import('./icons/book-user.js'),
  "book-x": () => import('./icons/book-x.js'),
  "book": () => import('./icons/book.js'),
  "bookmark-check": () => import('./icons/bookmark-check.js'),
  "bookmark-minus": () => import('./icons/bookmark-minus.js'),
  "bookmark-plus": () => import('./icons/bookmark-plus.js'),
  "bookmark-x": () => import('./icons/bookmark-x.js'),
  "bookmark": () => import('./icons/bookmark.js'),
  "boom-box": () => import('./icons/boom-box.js'),
  "bot-message-square": () => import('./icons/bot-message-square.js'),
  "bot-off": () => import('./icons/bot-off.js'),
  "bot": () => import('./icons/bot.js'),
  "box": () => import('./icons/box.js'),
  "boxes": () => import('./icons/boxes.js'),
  "braces": () => import('./icons/braces.js'),
  "curly-braces": () => import('./icons/braces.js'),
  "brackets": () => import('./icons/brackets.js'),
  "brain-circuit": () => import('./icons/brain-circuit.js'),
  "brain-cog": () => import('./icons/brain-cog.js'),
  "brain": () => import('./icons/brain.js'),
  "brick-wall": () => import('./icons/brick-wall.js'),
  "briefcase-business": () => import('./icons/briefcase-business.js'),
  "briefcase-conveyor-belt": () => import('./icons/briefcase-conveyor-belt.js'),
  "briefcase-medical": () => import('./icons/briefcase-medical.js'),
  "briefcase": () => import('./icons/briefcase.js'),
  "bring-to-front": () => import('./icons/bring-to-front.js'),
  "brush": () => import('./icons/brush.js'),
  "bug-off": () => import('./icons/bug-off.js'),
  "bug-play": () => import('./icons/bug-play.js'),
  "bug": () => import('./icons/bug.js'),
  "building-2": () => import('./icons/building-2.js'),
  "building": () => import('./icons/building.js'),
  "bus-front": () => import('./icons/bus-front.js'),
  "bus": () => import('./icons/bus.js'),
  "cable-car": () => import('./icons/cable-car.js'),
  "cable": () => import('./icons/cable.js'),
  "cake-slice": () => import('./icons/cake-slice.js'),
  "cake": () => import('./icons/cake.js'),
  "calculator": () => import('./icons/calculator.js'),
  "calendar-1": () => import('./icons/calendar-1.js'),
  "calendar-arrow-down": () => import('./icons/calendar-arrow-down.js'),
  "calendar-arrow-up": () => import('./icons/calendar-arrow-up.js'),
  "calendar-check-2": () => import('./icons/calendar-check-2.js'),
  "calendar-check": () => import('./icons/calendar-check.js'),
  "calendar-clock": () => import('./icons/calendar-clock.js'),
  "calendar-cog": () => import('./icons/calendar-cog.js'),
  "calendar-days": () => import('./icons/calendar-days.js'),
  "calendar-fold": () => import('./icons/calendar-fold.js'),
  "calendar-heart": () => import('./icons/calendar-heart.js'),
  "calendar-minus-2": () => import('./icons/calendar-minus-2.js'),
  "calendar-minus": () => import('./icons/calendar-minus.js'),
  "calendar-off": () => import('./icons/calendar-off.js'),
  "calendar-plus-2": () => import('./icons/calendar-plus-2.js'),
  "calendar-plus": () => import('./icons/calendar-plus.js'),
  "calendar-range": () => import('./icons/calendar-range.js'),
  "calendar-search": () => import('./icons/calendar-search.js'),
  "calendar-sync": () => import('./icons/calendar-sync.js'),
  "calendar-x-2": () => import('./icons/calendar-x-2.js'),
  "calendar-x": () => import('./icons/calendar-x.js'),
  "calendar": () => import('./icons/calendar.js'),
  "camera-off": () => import('./icons/camera-off.js'),
  "camera": () => import('./icons/camera.js'),
  "candy-cane": () => import('./icons/candy-cane.js'),
  "candy-off": () => import('./icons/candy-off.js'),
  "candy": () => import('./icons/candy.js'),
  "cannabis": () => import('./icons/cannabis.js'),
  "captions-off": () => import('./icons/captions-off.js'),
  "captions": () => import('./icons/captions.js'),
  "subtitles": () => import('./icons/captions.js'),
  "car-front": () => import('./icons/car-front.js'),
  "car-taxi-front": () => import('./icons/car-taxi-front.js'),
  "car": () => import('./icons/car.js'),
  "caravan": () => import('./icons/caravan.js'),
  "carrot": () => import('./icons/carrot.js'),
  "case-lower": () => import('./icons/case-lower.js'),
  "case-sensitive": () => import('./icons/case-sensitive.js'),
  "case-upper": () => import('./icons/case-upper.js'),
  "cassette-tape": () => import('./icons/cassette-tape.js'),
  "cast": () => import('./icons/cast.js'),
  "castle": () => import('./icons/castle.js'),
  "cat": () => import('./icons/cat.js'),
  "cctv": () => import('./icons/cctv.js'),
  "chart-area": () => import('./icons/chart-area.js'),
  "area-chart": () => import('./icons/chart-area.js'),
  "chart-bar-big": () => import('./icons/chart-bar-big.js'),
  "bar-chart-horizontal-big": () => import('./icons/chart-bar-big.js'),
  "chart-bar-decreasing": () => import('./icons/chart-bar-decreasing.js'),
  "chart-bar-increasing": () => import('./icons/chart-bar-increasing.js'),
  "chart-bar-stacked": () => import('./icons/chart-bar-stacked.js'),
  "chart-bar": () => import('./icons/chart-bar.js'),
  "bar-chart-horizontal": () => import('./icons/chart-bar.js'),
  "chart-candlestick": () => import('./icons/chart-candlestick.js'),
  "candlestick-chart": () => import('./icons/chart-candlestick.js'),
  "chart-column-big": () => import('./icons/chart-column-big.js'),
  "bar-chart-big": () => import('./icons/chart-column-big.js'),
  "chart-column-decreasing": () => import('./icons/chart-column-decreasing.js'),
  "chart-column-increasing": () => import('./icons/chart-column-increasing.js'),
  "bar-chart-4": () => import('./icons/chart-column-increasing.js'),
  "chart-column-stacked": () => import('./icons/chart-column-stacked.js'),
  "chart-column": () => import('./icons/chart-column.js'),
  "bar-chart-3": () => import('./icons/chart-column.js'),
  "chart-gantt": () => import('./icons/chart-gantt.js'),
  "chart-line": () => import('./icons/chart-line.js'),
  "line-chart": () => import('./icons/chart-line.js'),
  "chart-network": () => import('./icons/chart-network.js'),
  "chart-no-axes-column-decreasing": () => import('./icons/chart-no-axes-column-decreasing.js'),
  "chart-no-axes-column-increasing": () => import('./icons/chart-no-axes-column-increasing.js'),
  "bar-chart": () => import('./icons/chart-no-axes-column-increasing.js'),
  "chart-no-axes-column": () => import('./icons/chart-no-axes-column.js'),
  "bar-chart-2": () => import('./icons/chart-no-axes-column.js'),
  "chart-no-axes-combined": () => import('./icons/chart-no-axes-combined.js'),
  "chart-no-axes-gantt": () => import('./icons/chart-no-axes-gantt.js'),
  "gantt-chart": () => import('./icons/chart-no-axes-gantt.js'),
  "chart-pie": () => import('./icons/chart-pie.js'),
  "pie-chart": () => import('./icons/chart-pie.js'),
  "chart-scatter": () => import('./icons/chart-scatter.js'),
  "scatter-chart": () => import('./icons/chart-scatter.js'),
  "chart-spline": () => import('./icons/chart-spline.js'),
  "check-check": () => import('./icons/check-check.js'),
  "check": () => import('./icons/check.js'),
  "chef-hat": () => import('./icons/chef-hat.js'),
  "cherry": () => import('./icons/cherry.js'),
  "chevron-down": () => import('./icons/chevron-down.js'),
  "chevron-first": () => import('./icons/chevron-first.js'),
  "chevron-last": () => import('./icons/chevron-last.js'),
  "chevron-left": () => import('./icons/chevron-left.js'),
  "chevron-right": () => import('./icons/chevron-right.js'),
  "chevron-up": () => import('./icons/chevron-up.js'),
  "chevrons-down-up": () => import('./icons/chevrons-down-up.js'),
  "chevrons-down": () => import('./icons/chevrons-down.js'),
  "chevrons-left-right-ellipsis": () => import('./icons/chevrons-left-right-ellipsis.js'),
  "chevrons-left-right": () => import('./icons/chevrons-left-right.js'),
  "chevrons-left": () => import('./icons/chevrons-left.js'),
  "chevrons-right-left": () => import('./icons/chevrons-right-left.js'),
  "chevrons-right": () => import('./icons/chevrons-right.js'),
  "chevrons-up-down": () => import('./icons/chevrons-up-down.js'),
  "chevrons-up": () => import('./icons/chevrons-up.js'),
  "chrome": () => import('./icons/chrome.js'),
  "church": () => import('./icons/church.js'),
  "cigarette-off": () => import('./icons/cigarette-off.js'),
  "cigarette": () => import('./icons/cigarette.js'),
  "circle-alert": () => import('./icons/circle-alert.js'),
  "alert-circle": () => import('./icons/circle-alert.js'),
  "circle-arrow-down": () => import('./icons/circle-arrow-down.js'),
  "arrow-down-circle": () => import('./icons/circle-arrow-down.js'),
  "circle-arrow-left": () => import('./icons/circle-arrow-left.js'),
  "arrow-left-circle": () => import('./icons/circle-arrow-left.js'),
  "circle-arrow-out-down-left": () => import('./icons/circle-arrow-out-down-left.js'),
  "arrow-down-left-from-circle": () => import('./icons/circle-arrow-out-down-left.js'),
  "circle-arrow-out-down-right": () => import('./icons/circle-arrow-out-down-right.js'),
  "arrow-down-right-from-circle": () => import('./icons/circle-arrow-out-down-right.js'),
  "circle-arrow-out-up-left": () => import('./icons/circle-arrow-out-up-left.js'),
  "arrow-up-left-from-circle": () => import('./icons/circle-arrow-out-up-left.js'),
  "circle-arrow-out-up-right": () => import('./icons/circle-arrow-out-up-right.js'),
  "arrow-up-right-from-circle": () => import('./icons/circle-arrow-out-up-right.js'),
  "circle-arrow-right": () => import('./icons/circle-arrow-right.js'),
  "arrow-right-circle": () => import('./icons/circle-arrow-right.js'),
  "circle-arrow-up": () => import('./icons/circle-arrow-up.js'),
  "arrow-up-circle": () => import('./icons/circle-arrow-up.js'),
  "circle-check-big": () => import('./icons/circle-check-big.js'),
  "check-circle": () => import('./icons/circle-check-big.js'),
  "circle-check": () => import('./icons/circle-check.js'),
  "check-circle-2": () => import('./icons/circle-check.js'),
  "circle-chevron-down": () => import('./icons/circle-chevron-down.js'),
  "chevron-down-circle": () => import('./icons/circle-chevron-down.js'),
  "circle-chevron-left": () => import('./icons/circle-chevron-left.js'),
  "chevron-left-circle": () => import('./icons/circle-chevron-left.js'),
  "circle-chevron-right": () => import('./icons/circle-chevron-right.js'),
  "chevron-right-circle": () => import('./icons/circle-chevron-right.js'),
  "circle-chevron-up": () => import('./icons/circle-chevron-up.js'),
  "chevron-up-circle": () => import('./icons/circle-chevron-up.js'),
  "circle-dashed": () => import('./icons/circle-dashed.js'),
  "circle-divide": () => import('./icons/circle-divide.js'),
  "divide-circle": () => import('./icons/circle-divide.js'),
  "circle-dollar-sign": () => import('./icons/circle-dollar-sign.js'),
  "circle-dot-dashed": () => import('./icons/circle-dot-dashed.js'),
  "circle-dot": () => import('./icons/circle-dot.js'),
  "circle-ellipsis": () => import('./icons/circle-ellipsis.js'),
  "circle-equal": () => import('./icons/circle-equal.js'),
  "circle-fading-arrow-up": () => import('./icons/circle-fading-arrow-up.js'),
  "circle-fading-plus": () => import('./icons/circle-fading-plus.js'),
  "circle-gauge": () => import('./icons/circle-gauge.js'),
  "gauge-circle": () => import('./icons/circle-gauge.js'),
  "circle-help": () => import('./icons/circle-help.js'),
  "help-circle": () => import('./icons/circle-help.js'),
  "circle-minus": () => import('./icons/circle-minus.js'),
  "minus-circle": () => import('./icons/circle-minus.js'),
  "circle-off": () => import('./icons/circle-off.js'),
  "circle-parking-off": () => import('./icons/circle-parking-off.js'),
  "parking-circle-off": () => import('./icons/circle-parking-off.js'),
  "circle-parking": () => import('./icons/circle-parking.js'),
  "parking-circle": () => import('./icons/circle-parking.js'),
  "circle-pause": () => import('./icons/circle-pause.js'),
  "pause-circle": () => import('./icons/circle-pause.js'),
  "circle-percent": () => import('./icons/circle-percent.js'),
  "percent-circle": () => import('./icons/circle-percent.js'),
  "circle-play": () => import('./icons/circle-play.js'),
  "play-circle": () => import('./icons/circle-play.js'),
  "circle-plus": () => import('./icons/circle-plus.js'),
  "plus-circle": () => import('./icons/circle-plus.js'),
  "circle-power": () => import('./icons/circle-power.js'),
  "power-circle": () => import('./icons/circle-power.js'),
  "circle-slash-2": () => import('./icons/circle-slash-2.js'),
  "circle-slashed": () => import('./icons/circle-slash-2.js'),
  "circle-slash": () => import('./icons/circle-slash.js'),
  "circle-small": () => import('./icons/circle-small.js'),
  "circle-stop": () => import('./icons/circle-stop.js'),
  "stop-circle": () => import('./icons/circle-stop.js'),
  "circle-user-round": () => import('./icons/circle-user-round.js'),
  "user-circle-2": () => import('./icons/circle-user-round.js'),
  "circle-user": () => import('./icons/circle-user.js'),
  "user-circle": () => import('./icons/circle-user.js'),
  "circle-x": () => import('./icons/circle-x.js'),
  "x-circle": () => import('./icons/circle-x.js'),
  "circle": () => import('./icons/circle.js'),
  "circuit-board": () => import('./icons/circuit-board.js'),
  "citrus": () => import('./icons/citrus.js'),
  "clapperboard": () => import('./icons/clapperboard.js'),
  "clipboard-check": () => import('./icons/clipboard-check.js'),
  "clipboard-copy": () => import('./icons/clipboard-copy.js'),
  "clipboard-list": () => import('./icons/clipboard-list.js'),
  "clipboard-minus": () => import('./icons/clipboard-minus.js'),
  "clipboard-paste": () => import('./icons/clipboard-paste.js'),
  "clipboard-pen-line": () => import('./icons/clipboard-pen-line.js'),
  "clipboard-signature": () => import('./icons/clipboard-pen-line.js'),
  "clipboard-pen": () => import('./icons/clipboard-pen.js'),
  "clipboard-edit": () => import('./icons/clipboard-pen.js'),
  "clipboard-plus": () => import('./icons/clipboard-plus.js'),
  "clipboard-type": () => import('./icons/clipboard-type.js'),
  "clipboard-x": () => import('./icons/clipboard-x.js'),
  "clipboard": () => import('./icons/clipboard.js'),
  "clock-1": () => import('./icons/clock-1.js'),
  "clock-10": () => import('./icons/clock-10.js'),
  "clock-11": () => import('./icons/clock-11.js'),
  "clock-12": () => import('./icons/clock-12.js'),
  "clock-2": () => import('./icons/clock-2.js'),
  "clock-3": () => import('./icons/clock-3.js'),
  "clock-4": () => import('./icons/clock-4.js'),
  "clock-5": () => import('./icons/clock-5.js'),
  "clock-6": () => import('./icons/clock-6.js'),
  "clock-7": () => import('./icons/clock-7.js'),
  "clock-8": () => import('./icons/clock-8.js'),
  "clock-9": () => import('./icons/clock-9.js'),
  "clock-alert": () => import('./icons/clock-alert.js'),
  "clock-arrow-down": () => import('./icons/clock-arrow-down.js'),
  "clock-arrow-up": () => import('./icons/clock-arrow-up.js'),
  "clock-fading": () => import('./icons/clock-fading.js'),
  "clock": () => import('./icons/clock.js'),
  "cloud-alert": () => import('./icons/cloud-alert.js'),
  "cloud-cog": () => import('./icons/cloud-cog.js'),
  "cloud-download": () => import('./icons/cloud-download.js'),
  "download-cloud": () => import('./icons/cloud-download.js'),
  "cloud-drizzle": () => import('./icons/cloud-drizzle.js'),
  "cloud-fog": () => import('./icons/cloud-fog.js'),
  "cloud-hail": () => import('./icons/cloud-hail.js'),
  "cloud-lightning": () => import('./icons/cloud-lightning.js'),
  "cloud-moon-rain": () => import('./icons/cloud-moon-rain.js'),
  "cloud-moon": () => import('./icons/cloud-moon.js'),
  "cloud-off": () => import('./icons/cloud-off.js'),
  "cloud-rain-wind": () => import('./icons/cloud-rain-wind.js'),
  "cloud-rain": () => import('./icons/cloud-rain.js'),
  "cloud-snow": () => import('./icons/cloud-snow.js'),
  "cloud-sun-rain": () => import('./icons/cloud-sun-rain.js'),
  "cloud-sun": () => import('./icons/cloud-sun.js'),
  "cloud-upload": () => import('./icons/cloud-upload.js'),
  "upload-cloud": () => import('./icons/cloud-upload.js'),
  "cloud": () => import('./icons/cloud.js'),
  "cloudy": () => import('./icons/cloudy.js'),
  "clover": () => import('./icons/clover.js'),
  "club": () => import('./icons/club.js'),
  "code-xml": () => import('./icons/code-xml.js'),
  "code-2": () => import('./icons/code-xml.js'),
  "code": () => import('./icons/code.js'),
  "codepen": () => import('./icons/codepen.js'),
  "codesandbox": () => import('./icons/codesandbox.js'),
  "coffee": () => import('./icons/coffee.js'),
  "cog": () => import('./icons/cog.js'),
  "coins": () => import('./icons/coins.js'),
  "columns-2": () => import('./icons/columns-2.js'),
  "columns": () => import('./icons/columns-2.js'),
  "columns-3": () => import('./icons/columns-3.js'),
  "panels-left-right": () => import('./icons/columns-3.js'),
  "columns-4": () => import('./icons/columns-4.js'),
  "combine": () => import('./icons/combine.js'),
  "command": () => import('./icons/command.js'),
  "compass": () => import('./icons/compass.js'),
  "component": () => import('./icons/component.js'),
  "computer": () => import('./icons/computer.js'),
  "concierge-bell": () => import('./icons/concierge-bell.js'),
  "cone": () => import('./icons/cone.js'),
  "construction": () => import('./icons/construction.js'),
  "contact-round": () => import('./icons/contact-round.js'),
  "contact-2": () => import('./icons/contact-round.js'),
  "contact": () => import('./icons/contact.js'),
  "container": () => import('./icons/container.js'),
  "contrast": () => import('./icons/contrast.js'),
  "cookie": () => import('./icons/cookie.js'),
  "cooking-pot": () => import('./icons/cooking-pot.js'),
  "copy-check": () => import('./icons/copy-check.js'),
  "copy-minus": () => import('./icons/copy-minus.js'),
  "copy-plus": () => import('./icons/copy-plus.js'),
  "copy-slash": () => import('./icons/copy-slash.js'),
  "copy-x": () => import('./icons/copy-x.js'),
  "copy": () => import('./icons/copy.js'),
  "copyleft": () => import('./icons/copyleft.js'),
  "copyright": () => import('./icons/copyright.js'),
  "corner-down-left": () => import('./icons/corner-down-left.js'),
  "corner-down-right": () => import('./icons/corner-down-right.js'),
  "corner-left-down": () => import('./icons/corner-left-down.js'),
  "corner-left-up": () => import('./icons/corner-left-up.js'),
  "corner-right-down": () => import('./icons/corner-right-down.js'),
  "corner-right-up": () => import('./icons/corner-right-up.js'),
  "corner-up-left": () => import('./icons/corner-up-left.js'),
  "corner-up-right": () => import('./icons/corner-up-right.js'),
  "cpu": () => import('./icons/cpu.js'),
  "creative-commons": () => import('./icons/creative-commons.js'),
  "credit-card": () => import('./icons/credit-card.js'),
  "croissant": () => import('./icons/croissant.js'),
  "crop": () => import('./icons/crop.js'),
  "cross": () => import('./icons/cross.js'),
  "crosshair": () => import('./icons/crosshair.js'),
  "crown": () => import('./icons/crown.js'),
  "cuboid": () => import('./icons/cuboid.js'),
  "cup-soda": () => import('./icons/cup-soda.js'),
  "currency": () => import('./icons/currency.js'),
  "cylinder": () => import('./icons/cylinder.js'),
  "dam": () => import('./icons/dam.js'),
  "database-backup": () => import('./icons/database-backup.js'),
  "database-zap": () => import('./icons/database-zap.js'),
  "database": () => import('./icons/database.js'),
  "delete": () => import('./icons/delete.js'),
  "dessert": () => import('./icons/dessert.js'),
  "diameter": () => import('./icons/diameter.js'),
  "diamond-minus": () => import('./icons/diamond-minus.js'),
  "diamond-percent": () => import('./icons/diamond-percent.js'),
  "percent-diamond": () => import('./icons/diamond-percent.js'),
  "diamond-plus": () => import('./icons/diamond-plus.js'),
  "diamond": () => import('./icons/diamond.js'),
  "dice-1": () => import('./icons/dice-1.js'),
  "dice-2": () => import('./icons/dice-2.js'),
  "dice-3": () => import('./icons/dice-3.js'),
  "dice-4": () => import('./icons/dice-4.js'),
  "dice-5": () => import('./icons/dice-5.js'),
  "dice-6": () => import('./icons/dice-6.js'),
  "dices": () => import('./icons/dices.js'),
  "diff": () => import('./icons/diff.js'),
  "disc-2": () => import('./icons/disc-2.js'),
  "disc-3": () => import('./icons/disc-3.js'),
  "disc-album": () => import('./icons/disc-album.js'),
  "disc": () => import('./icons/disc.js'),
  "divide": () => import('./icons/divide.js'),
  "dna-off": () => import('./icons/dna-off.js'),
  "dna": () => import('./icons/dna.js'),
  "dock": () => import('./icons/dock.js'),
  "dog": () => import('./icons/dog.js'),
  "dollar-sign": () => import('./icons/dollar-sign.js'),
  "donut": () => import('./icons/donut.js'),
  "door-closed": () => import('./icons/door-closed.js'),
  "door-open": () => import('./icons/door-open.js'),
  "dot": () => import('./icons/dot.js'),
  "download": () => import('./icons/download.js'),
  "drafting-compass": () => import('./icons/drafting-compass.js'),
  "drama": () => import('./icons/drama.js'),
  "dribbble": () => import('./icons/dribbble.js'),
  "drill": () => import('./icons/drill.js'),
  "droplet-off": () => import('./icons/droplet-off.js'),
  "droplet": () => import('./icons/droplet.js'),
  "droplets": () => import('./icons/droplets.js'),
  "drum": () => import('./icons/drum.js'),
  "drumstick": () => import('./icons/drumstick.js'),
  "dumbbell": () => import('./icons/dumbbell.js'),
  "ear-off": () => import('./icons/ear-off.js'),
  "ear": () => import('./icons/ear.js'),
  "earth-lock": () => import('./icons/earth-lock.js'),
  "earth": () => import('./icons/earth.js'),
  "globe-2": () => import('./icons/earth.js'),
  "eclipse": () => import('./icons/eclipse.js'),
  "egg-fried": () => import('./icons/egg-fried.js'),
  "egg-off": () => import('./icons/egg-off.js'),
  "egg": () => import('./icons/egg.js'),
  "ellipsis-vertical": () => import('./icons/ellipsis-vertical.js'),
  "more-vertical": () => import('./icons/ellipsis-vertical.js'),
  "ellipsis": () => import('./icons/ellipsis.js'),
  "more-horizontal": () => import('./icons/ellipsis.js'),
  "equal-approximately": () => import('./icons/equal-approximately.js'),
  "equal-not": () => import('./icons/equal-not.js'),
  "equal": () => import('./icons/equal.js'),
  "eraser": () => import('./icons/eraser.js'),
  "ethernet-port": () => import('./icons/ethernet-port.js'),
  "euro": () => import('./icons/euro.js'),
  "expand": () => import('./icons/expand.js'),
  "external-link": () => import('./icons/external-link.js'),
  "eye-closed": () => import('./icons/eye-closed.js'),
  "eye-off": () => import('./icons/eye-off.js'),
  "eye": () => import('./icons/eye.js'),
  "facebook": () => import('./icons/facebook.js'),
  "factory": () => import('./icons/factory.js'),
  "fan": () => import('./icons/fan.js'),
  "fast-forward": () => import('./icons/fast-forward.js'),
  "feather": () => import('./icons/feather.js'),
  "fence": () => import('./icons/fence.js'),
  "ferris-wheel": () => import('./icons/ferris-wheel.js'),
  "figma": () => import('./icons/figma.js'),
  "file-archive": () => import('./icons/file-archive.js'),
  "file-audio-2": () => import('./icons/file-audio-2.js'),
  "file-audio": () => import('./icons/file-audio.js'),
  "file-axis-3d": () => import('./icons/file-axis-3d.js'),
  "file-axis-3-d": () => import('./icons/file-axis-3d.js'),
  "file-badge-2": () => import('./icons/file-badge-2.js'),
  "file-badge": () => import('./icons/file-badge.js'),
  "file-box": () => import('./icons/file-box.js'),
  "file-chart-column-increasing": () => import('./icons/file-chart-column-increasing.js'),
  "file-bar-chart": () => import('./icons/file-chart-column-increasing.js'),
  "file-chart-column": () => import('./icons/file-chart-column.js'),
  "file-bar-chart-2": () => import('./icons/file-chart-column.js'),
  "file-chart-line": () => import('./icons/file-chart-line.js'),
  "file-line-chart": () => import('./icons/file-chart-line.js'),
  "file-chart-pie": () => import('./icons/file-chart-pie.js'),
  "file-pie-chart": () => import('./icons/file-chart-pie.js'),
  "file-check-2": () => import('./icons/file-check-2.js'),
  "file-check": () => import('./icons/file-check.js'),
  "file-clock": () => import('./icons/file-clock.js'),
  "file-code-2": () => import('./icons/file-code-2.js'),
  "file-code": () => import('./icons/file-code.js'),
  "file-cog": () => import('./icons/file-cog.js'),
  "file-cog-2": () => import('./icons/file-cog.js'),
  "file-diff": () => import('./icons/file-diff.js'),
  "file-digit": () => import('./icons/file-digit.js'),
  "file-down": () => import('./icons/file-down.js'),
  "file-heart": () => import('./icons/file-heart.js'),
  "file-image": () => import('./icons/file-image.js'),
  "file-input": () => import('./icons/file-input.js'),
  "file-json-2": () => import('./icons/file-json-2.js'),
  "file-json": () => import('./icons/file-json.js'),
  "file-key-2": () => import('./icons/file-key-2.js'),
  "file-key": () => import('./icons/file-key.js'),
  "file-lock-2": () => import('./icons/file-lock-2.js'),
  "file-lock": () => import('./icons/file-lock.js'),
  "file-minus-2": () => import('./icons/file-minus-2.js'),
  "file-minus": () => import('./icons/file-minus.js'),
  "file-music": () => import('./icons/file-music.js'),
  "file-output": () => import('./icons/file-output.js'),
  "file-pen-line": () => import('./icons/file-pen-line.js'),
  "file-signature": () => import('./icons/file-pen-line.js'),
  "file-pen": () => import('./icons/file-pen.js'),
  "file-edit": () => import('./icons/file-pen.js'),
  "file-plus-2": () => import('./icons/file-plus-2.js'),
  "file-plus": () => import('./icons/file-plus.js'),
  "file-question": () => import('./icons/file-question.js'),
  "file-scan": () => import('./icons/file-scan.js'),
  "file-search-2": () => import('./icons/file-search-2.js'),
  "file-search": () => import('./icons/file-search.js'),
  "file-sliders": () => import('./icons/file-sliders.js'),
  "file-spreadsheet": () => import('./icons/file-spreadsheet.js'),
  "file-stack": () => import('./icons/file-stack.js'),
  "file-symlink": () => import('./icons/file-symlink.js'),
  "file-terminal": () => import('./icons/file-terminal.js'),
  "file-text": () => import('./icons/file-text.js'),
  "file-type-2": () => import('./icons/file-type-2.js'),
  "file-type": () => import('./icons/file-type.js'),
  "file-up": () => import('./icons/file-up.js'),
  "file-user": () => import('./icons/file-user.js'),
  "file-video-2": () => import('./icons/file-video-2.js'),
  "file-video": () => import('./icons/file-video.js'),
  "file-volume-2": () => import('./icons/file-volume-2.js'),
  "file-volume": () => import('./icons/file-volume.js'),
  "file-warning": () => import('./icons/file-warning.js'),
  "file-x-2": () => import('./icons/file-x-2.js'),
  "file-x": () => import('./icons/file-x.js'),
  "file": () => import('./icons/file.js'),
  "files": () => import('./icons/files.js'),
  "film": () => import('./icons/film.js'),
  "fingerprint": () => import('./icons/fingerprint.js'),
  "fire-extinguisher": () => import('./icons/fire-extinguisher.js'),
  "fish-off": () => import('./icons/fish-off.js'),
  "fish-symbol": () => import('./icons/fish-symbol.js'),
  "fish": () => import('./icons/fish.js'),
  "flag-off": () => import('./icons/flag-off.js'),
  "flag-triangle-left": () => import('./icons/flag-triangle-left.js'),
  "flag-triangle-right": () => import('./icons/flag-triangle-right.js'),
  "flag": () => import('./icons/flag.js'),
  "flame-kindling": () => import('./icons/flame-kindling.js'),
  "flame": () => import('./icons/flame.js'),
  "flashlight-off": () => import('./icons/flashlight-off.js'),
  "flashlight": () => import('./icons/flashlight.js'),
  "flask-conical-off": () => import('./icons/flask-conical-off.js'),
  "flask-conical": () => import('./icons/flask-conical.js'),
  "flask-round": () => import('./icons/flask-round.js'),
  "flip-horizontal-2": () => import('./icons/flip-horizontal-2.js'),
  "flip-horizontal": () => import('./icons/flip-horizontal.js'),
  "flip-vertical-2": () => import('./icons/flip-vertical-2.js'),
  "flip-vertical": () => import('./icons/flip-vertical.js'),
  "flower-2": () => import('./icons/flower-2.js'),
  "flower": () => import('./icons/flower.js'),
  "focus": () => import('./icons/focus.js'),
  "fold-horizontal": () => import('./icons/fold-horizontal.js'),
  "fold-vertical": () => import('./icons/fold-vertical.js'),
  "folder-archive": () => import('./icons/folder-archive.js'),
  "folder-check": () => import('./icons/folder-check.js'),
  "folder-clock": () => import('./icons/folder-clock.js'),
  "folder-closed": () => import('./icons/folder-closed.js'),
  "folder-code": () => import('./icons/folder-code.js'),
  "folder-cog": () => import('./icons/folder-cog.js'),
  "folder-cog-2": () => import('./icons/folder-cog.js'),
  "folder-dot": () => import('./icons/folder-dot.js'),
  "folder-down": () => import('./icons/folder-down.js'),
  "folder-git-2": () => import('./icons/folder-git-2.js'),
  "folder-git": () => import('./icons/folder-git.js'),
  "folder-heart": () => import('./icons/folder-heart.js'),
  "folder-input": () => import('./icons/folder-input.js'),
  "folder-kanban": () => import('./icons/folder-kanban.js'),
  "folder-key": () => import('./icons/folder-key.js'),
  "folder-lock": () => import('./icons/folder-lock.js'),
  "folder-minus": () => import('./icons/folder-minus.js'),
  "folder-open-dot": () => import('./icons/folder-open-dot.js'),
  "folder-open": () => import('./icons/folder-open.js'),
  "folder-output": () => import('./icons/folder-output.js'),
  "folder-pen": () => import('./icons/folder-pen.js'),
  "folder-edit": () => import('./icons/folder-pen.js'),
  "folder-plus": () => import('./icons/folder-plus.js'),
  "folder-root": () => import('./icons/folder-root.js'),
  "folder-search-2": () => import('./icons/folder-search-2.js'),
  "folder-search": () => import('./icons/folder-search.js'),
  "folder-symlink": () => import('./icons/folder-symlink.js'),
  "folder-sync": () => import('./icons/folder-sync.js'),
  "folder-tree": () => import('./icons/folder-tree.js'),
  "folder-up": () => import('./icons/folder-up.js'),
  "folder-x": () => import('./icons/folder-x.js'),
  "folder": () => import('./icons/folder.js'),
  "folders": () => import('./icons/folders.js'),
  "footprints": () => import('./icons/footprints.js'),
  "forklift": () => import('./icons/forklift.js'),
  "forward": () => import('./icons/forward.js'),
  "frame": () => import('./icons/frame.js'),
  "framer": () => import('./icons/framer.js'),
  "frown": () => import('./icons/frown.js'),
  "fuel": () => import('./icons/fuel.js'),
  "fullscreen": () => import('./icons/fullscreen.js'),
  "funnel-plus": () => import('./icons/funnel-plus.js'),
  "funnel-x": () => import('./icons/funnel-x.js'),
  "filter-x": () => import('./icons/funnel-x.js'),
  "funnel": () => import('./icons/funnel.js'),
  "filter": () => import('./icons/funnel.js'),
  "gallery-horizontal-end": () => import('./icons/gallery-horizontal-end.js'),
  "gallery-horizontal": () => import('./icons/gallery-horizontal.js'),
  "gallery-thumbnails": () => import('./icons/gallery-thumbnails.js'),
  "gallery-vertical-end": () => import('./icons/gallery-vertical-end.js'),
  "gallery-vertical": () => import('./icons/gallery-vertical.js'),
  "gamepad-2": () => import('./icons/gamepad-2.js'),
  "gamepad": () => import('./icons/gamepad.js'),
  "gauge": () => import('./icons/gauge.js'),
  "gavel": () => import('./icons/gavel.js'),
  "gem": () => import('./icons/gem.js'),
  "ghost": () => import('./icons/ghost.js'),
  "gift": () => import('./icons/gift.js'),
  "git-branch-plus": () => import('./icons/git-branch-plus.js'),
  "git-branch": () => import('./icons/git-branch.js'),
  "git-commit-horizontal": () => import('./icons/git-commit-horizontal.js'),
  "git-commit": () => import('./icons/git-commit-horizontal.js'),
  "git-commit-vertical": () => import('./icons/git-commit-vertical.js'),
  "git-compare-arrows": () => import('./icons/git-compare-arrows.js'),
  "git-compare": () => import('./icons/git-compare.js'),
  "git-fork": () => import('./icons/git-fork.js'),
  "git-graph": () => import('./icons/git-graph.js'),
  "git-merge": () => import('./icons/git-merge.js'),
  "git-pull-request-arrow": () => import('./icons/git-pull-request-arrow.js'),
  "git-pull-request-closed": () => import('./icons/git-pull-request-closed.js'),
  "git-pull-request-create-arrow": () => import('./icons/git-pull-request-create-arrow.js'),
  "git-pull-request-create": () => import('./icons/git-pull-request-create.js'),
  "git-pull-request-draft": () => import('./icons/git-pull-request-draft.js'),
  "git-pull-request": () => import('./icons/git-pull-request.js'),
  "github": () => import('./icons/github.js'),
  "gitlab": () => import('./icons/gitlab.js'),
  "glass-water": () => import('./icons/glass-water.js'),
  "glasses": () => import('./icons/glasses.js'),
  "globe-lock": () => import('./icons/globe-lock.js'),
  "globe": () => import('./icons/globe.js'),
  "goal": () => import('./icons/goal.js'),
  "grab": () => import('./icons/grab.js'),
  "graduation-cap": () => import('./icons/graduation-cap.js'),
  "grape": () => import('./icons/grape.js'),
  "grid-2x2-check": () => import('./icons/grid-2x2-check.js'),
  "grid-2-x-2-check": () => import('./icons/grid-2x2-check.js'),
  "grid-2x2-plus": () => import('./icons/grid-2x2-plus.js'),
  "grid-2-x-2-plus": () => import('./icons/grid-2x2-plus.js'),
  "grid-2x2-x": () => import('./icons/grid-2x2-x.js'),
  "grid-2-x-2-x": () => import('./icons/grid-2x2-x.js'),
  "grid-2x2": () => import('./icons/grid-2x2.js'),
  "grid-2-x-2": () => import('./icons/grid-2x2.js'),
  "grid-3x3": () => import('./icons/grid-3x3.js'),
  "grid": () => import('./icons/grid-3x3.js'),
  "grid-3-x-3": () => import('./icons/grid-3x3.js'),
  "grip-horizontal": () => import('./icons/grip-horizontal.js'),
  "grip-vertical": () => import('./icons/grip-vertical.js'),
  "grip": () => import('./icons/grip.js'),
  "group": () => import('./icons/group.js'),
  "guitar": () => import('./icons/guitar.js'),
  "ham": () => import('./icons/ham.js'),
  "hammer": () => import('./icons/hammer.js'),
  "hand-coins": () => import('./icons/hand-coins.js'),
  "hand-heart": () => import('./icons/hand-heart.js'),
  "hand-helping": () => import('./icons/hand-helping.js'),
  "helping-hand": () => import('./icons/hand-helping.js'),
  "hand-metal": () => import('./icons/hand-metal.js'),
  "hand-platter": () => import('./icons/hand-platter.js'),
  "hand": () => import('./icons/hand.js'),
  "handshake": () => import('./icons/handshake.js'),
  "hard-drive-download": () => import('./icons/hard-drive-download.js'),
  "hard-drive-upload": () => import('./icons/hard-drive-upload.js'),
  "hard-drive": () => import('./icons/hard-drive.js'),
  "hard-hat": () => import('./icons/hard-hat.js'),
  "hash": () => import('./icons/hash.js'),
  "haze": () => import('./icons/haze.js'),
  "hdmi-port": () => import('./icons/hdmi-port.js'),
  "heading-1": () => import('./icons/heading-1.js'),
  "heading-2": () => import('./icons/heading-2.js'),
  "heading-3": () => import('./icons/heading-3.js'),
  "heading-4": () => import('./icons/heading-4.js'),
  "heading-5": () => import('./icons/heading-5.js'),
  "heading-6": () => import('./icons/heading-6.js'),
  "heading": () => import('./icons/heading.js'),
  "headphone-off": () => import('./icons/headphone-off.js'),
  "headphones": () => import('./icons/headphones.js'),
  "headset": () => import('./icons/headset.js'),
  "heart-crack": () => import('./icons/heart-crack.js'),
  "heart-handshake": () => import('./icons/heart-handshake.js'),
  "heart-off": () => import('./icons/heart-off.js'),
  "heart-pulse": () => import('./icons/heart-pulse.js'),
  "heart": () => import('./icons/heart.js'),
  "heater": () => import('./icons/heater.js'),
  "hexagon": () => import('./icons/hexagon.js'),
  "highlighter": () => import('./icons/highlighter.js'),
  "history": () => import('./icons/history.js'),
  "hop-off": () => import('./icons/hop-off.js'),
  "hop": () => import('./icons/hop.js'),
  "hospital": () => import('./icons/hospital.js'),
  "hotel": () => import('./icons/hotel.js'),
  "hourglass": () => import('./icons/hourglass.js'),
  "house-plug": () => import('./icons/house-plug.js'),
  "house-plus": () => import('./icons/house-plus.js'),
  "house-wifi": () => import('./icons/house-wifi.js'),
  "house": () => import('./icons/house.js'),
  "home": () => import('./icons/house.js'),
  "ice-cream-bowl": () => import('./icons/ice-cream-bowl.js'),
  "ice-cream-2": () => import('./icons/ice-cream-bowl.js'),
  "ice-cream-cone": () => import('./icons/ice-cream-cone.js'),
  "ice-cream": () => import('./icons/ice-cream-cone.js'),
  "id-card": () => import('./icons/id-card.js'),
  "image-down": () => import('./icons/image-down.js'),
  "image-minus": () => import('./icons/image-minus.js'),
  "image-off": () => import('./icons/image-off.js'),
  "image-play": () => import('./icons/image-play.js'),
  "image-plus": () => import('./icons/image-plus.js'),
  "image-up": () => import('./icons/image-up.js'),
  "image-upscale": () => import('./icons/image-upscale.js'),
  "image": () => import('./icons/image.js'),
  "images": () => import('./icons/images.js'),
  "import": () => import('./icons/import.js'),
  "inbox": () => import('./icons/inbox.js'),
  "indent-decrease": () => import('./icons/indent-decrease.js'),
  "outdent": () => import('./icons/indent-decrease.js'),
  "indent-increase": () => import('./icons/indent-increase.js'),
  "indent": () => import('./icons/indent-increase.js'),
  "indian-rupee": () => import('./icons/indian-rupee.js'),
  "infinity": () => import('./icons/infinity.js'),
  "info": () => import('./icons/info.js'),
  "inspection-panel": () => import('./icons/inspection-panel.js'),
  "instagram": () => import('./icons/instagram.js'),
  "italic": () => import('./icons/italic.js'),
  "iteration-ccw": () => import('./icons/iteration-ccw.js'),
  "iteration-cw": () => import('./icons/iteration-cw.js'),
  "japanese-yen": () => import('./icons/japanese-yen.js'),
  "joystick": () => import('./icons/joystick.js'),
  "kanban": () => import('./icons/kanban.js'),
  "key-round": () => import('./icons/key-round.js'),
  "key-square": () => import('./icons/key-square.js'),
  "key": () => import('./icons/key.js'),
  "keyboard-music": () => import('./icons/keyboard-music.js'),
  "keyboard-off": () => import('./icons/keyboard-off.js'),
  "keyboard": () => import('./icons/keyboard.js'),
  "lamp-ceiling": () => import('./icons/lamp-ceiling.js'),
  "lamp-desk": () => import('./icons/lamp-desk.js'),
  "lamp-floor": () => import('./icons/lamp-floor.js'),
  "lamp-wall-down": () => import('./icons/lamp-wall-down.js'),
  "lamp-wall-up": () => import('./icons/lamp-wall-up.js'),
  "lamp": () => import('./icons/lamp.js'),
  "land-plot": () => import('./icons/land-plot.js'),
  "landmark": () => import('./icons/landmark.js'),
  "languages": () => import('./icons/languages.js'),
  "laptop-minimal-check": () => import('./icons/laptop-minimal-check.js'),
  "laptop-minimal": () => import('./icons/laptop-minimal.js'),
  "laptop-2": () => import('./icons/laptop-minimal.js'),
  "laptop": () => import('./icons/laptop.js'),
  "lasso-select": () => import('./icons/lasso-select.js'),
  "lasso": () => import('./icons/lasso.js'),
  "laugh": () => import('./icons/laugh.js'),
  "layers-2": () => import('./icons/layers-2.js'),
  "layers": () => import('./icons/layers.js'),
  "layers-3": () => import('./icons/layers.js'),
  "layout-dashboard": () => import('./icons/layout-dashboard.js'),
  "layout-grid": () => import('./icons/layout-grid.js'),
  "layout-list": () => import('./icons/layout-list.js'),
  "layout-panel-left": () => import('./icons/layout-panel-left.js'),
  "layout-panel-top": () => import('./icons/layout-panel-top.js'),
  "layout-template": () => import('./icons/layout-template.js'),
  "leaf": () => import('./icons/leaf.js'),
  "leafy-green": () => import('./icons/leafy-green.js'),
  "lectern": () => import('./icons/lectern.js'),
  "letter-text": () => import('./icons/letter-text.js'),
  "library-big": () => import('./icons/library-big.js'),
  "library": () => import('./icons/library.js'),
  "life-buoy": () => import('./icons/life-buoy.js'),
  "ligature": () => import('./icons/ligature.js'),
  "lightbulb-off": () => import('./icons/lightbulb-off.js'),
  "lightbulb": () => import('./icons/lightbulb.js'),
  "link-2-off": () => import('./icons/link-2-off.js'),
  "link-2": () => import('./icons/link-2.js'),
  "link": () => import('./icons/link.js'),
  "linkedin": () => import('./icons/linkedin.js'),
  "list-check": () => import('./icons/list-check.js'),
  "list-checks": () => import('./icons/list-checks.js'),
  "list-collapse": () => import('./icons/list-collapse.js'),
  "list-end": () => import('./icons/list-end.js'),
  "list-filter-plus": () => import('./icons/list-filter-plus.js'),
  "list-filter": () => import('./icons/list-filter.js'),
  "list-minus": () => import('./icons/list-minus.js'),
  "list-music": () => import('./icons/list-music.js'),
  "list-ordered": () => import('./icons/list-ordered.js'),
  "list-plus": () => import('./icons/list-plus.js'),
  "list-restart": () => import('./icons/list-restart.js'),
  "list-start": () => import('./icons/list-start.js'),
  "list-todo": () => import('./icons/list-todo.js'),
  "list-tree": () => import('./icons/list-tree.js'),
  "list-video": () => import('./icons/list-video.js'),
  "list-x": () => import('./icons/list-x.js'),
  "list": () => import('./icons/list.js'),
  "loader-circle": () => import('./icons/loader-circle.js'),
  "loader-2": () => import('./icons/loader-circle.js'),
  "loader-pinwheel": () => import('./icons/loader-pinwheel.js'),
  "loader": () => import('./icons/loader.js'),
  "locate-fixed": () => import('./icons/locate-fixed.js'),
  "locate-off": () => import('./icons/locate-off.js'),
  "locate": () => import('./icons/locate.js'),
  "lock-keyhole-open": () => import('./icons/lock-keyhole-open.js'),
  "unlock-keyhole": () => import('./icons/lock-keyhole-open.js'),
  "lock-keyhole": () => import('./icons/lock-keyhole.js'),
  "lock-open": () => import('./icons/lock-open.js'),
  "unlock": () => import('./icons/lock-open.js'),
  "lock": () => import('./icons/lock.js'),
  "log-in": () => import('./icons/log-in.js'),
  "log-out": () => import('./icons/log-out.js'),
  "logs": () => import('./icons/logs.js'),
  "lollipop": () => import('./icons/lollipop.js'),
  "luggage": () => import('./icons/luggage.js'),
  "magnet": () => import('./icons/magnet.js'),
  "mail-check": () => import('./icons/mail-check.js'),
  "mail-minus": () => import('./icons/mail-minus.js'),
  "mail-open": () => import('./icons/mail-open.js'),
  "mail-plus": () => import('./icons/mail-plus.js'),
  "mail-question": () => import('./icons/mail-question.js'),
  "mail-search": () => import('./icons/mail-search.js'),
  "mail-warning": () => import('./icons/mail-warning.js'),
  "mail-x": () => import('./icons/mail-x.js'),
  "mail": () => import('./icons/mail.js'),
  "mailbox": () => import('./icons/mailbox.js'),
  "mails": () => import('./icons/mails.js'),
  "map-pin-check-inside": () => import('./icons/map-pin-check-inside.js'),
  "map-pin-check": () => import('./icons/map-pin-check.js'),
  "map-pin-house": () => import('./icons/map-pin-house.js'),
  "map-pin-minus-inside": () => import('./icons/map-pin-minus-inside.js'),
  "map-pin-minus": () => import('./icons/map-pin-minus.js'),
  "map-pin-off": () => import('./icons/map-pin-off.js'),
  "map-pin-plus-inside": () => import('./icons/map-pin-plus-inside.js'),
  "map-pin-plus": () => import('./icons/map-pin-plus.js'),
  "map-pin-x-inside": () => import('./icons/map-pin-x-inside.js'),
  "map-pin-x": () => import('./icons/map-pin-x.js'),
  "map-pin": () => import('./icons/map-pin.js'),
  "map-pinned": () => import('./icons/map-pinned.js'),
  "map-plus": () => import('./icons/map-plus.js'),
  "map": () => import('./icons/map.js'),
  "mars-stroke": () => import('./icons/mars-stroke.js'),
  "mars": () => import('./icons/mars.js'),
  "martini": () => import('./icons/martini.js'),
  "maximize-2": () => import('./icons/maximize-2.js'),
  "maximize": () => import('./icons/maximize.js'),
  "medal": () => import('./icons/medal.js'),
  "megaphone-off": () => import('./icons/megaphone-off.js'),
  "megaphone": () => import('./icons/megaphone.js'),
  "meh": () => import('./icons/meh.js'),
  "memory-stick": () => import('./icons/memory-stick.js'),
  "menu": () => import('./icons/menu.js'),
  "merge": () => import('./icons/merge.js'),
  "message-circle-code": () => import('./icons/message-circle-code.js'),
  "message-circle-dashed": () => import('./icons/message-circle-dashed.js'),
  "message-circle-heart": () => import('./icons/message-circle-heart.js'),
  "message-circle-more": () => import('./icons/message-circle-more.js'),
  "message-circle-off": () => import('./icons/message-circle-off.js'),
  "message-circle-plus": () => import('./icons/message-circle-plus.js'),
  "message-circle-question": () => import('./icons/message-circle-question.js'),
  "message-circle-reply": () => import('./icons/message-circle-reply.js'),
  "message-circle-warning": () => import('./icons/message-circle-warning.js'),
  "message-circle-x": () => import('./icons/message-circle-x.js'),
  "message-circle": () => import('./icons/message-circle.js'),
  "message-square-code": () => import('./icons/message-square-code.js'),
  "message-square-dashed": () => import('./icons/message-square-dashed.js'),
  "message-square-diff": () => import('./icons/message-square-diff.js'),
  "message-square-dot": () => import('./icons/message-square-dot.js'),
  "message-square-heart": () => import('./icons/message-square-heart.js'),
  "message-square-lock": () => import('./icons/message-square-lock.js'),
  "message-square-more": () => import('./icons/message-square-more.js'),
  "message-square-off": () => import('./icons/message-square-off.js'),
  "message-square-plus": () => import('./icons/message-square-plus.js'),
  "message-square-quote": () => import('./icons/message-square-quote.js'),
  "message-square-reply": () => import('./icons/message-square-reply.js'),
  "message-square-share": () => import('./icons/message-square-share.js'),
  "message-square-text": () => import('./icons/message-square-text.js'),
  "message-square-warning": () => import('./icons/message-square-warning.js'),
  "message-square-x": () => import('./icons/message-square-x.js'),
  "message-square": () => import('./icons/message-square.js'),
  "messages-square": () => import('./icons/messages-square.js'),
  "mic-off": () => import('./icons/mic-off.js'),
  "mic-vocal": () => import('./icons/mic-vocal.js'),
  "mic-2": () => import('./icons/mic-vocal.js'),
  "mic": () => import('./icons/mic.js'),
  "microchip": () => import('./icons/microchip.js'),
  "microscope": () => import('./icons/microscope.js'),
  "microwave": () => import('./icons/microwave.js'),
  "milestone": () => import('./icons/milestone.js'),
  "milk-off": () => import('./icons/milk-off.js'),
  "milk": () => import('./icons/milk.js'),
  "minimize-2": () => import('./icons/minimize-2.js'),
  "minimize": () => import('./icons/minimize.js'),
  "minus": () => import('./icons/minus.js'),
  "monitor-check": () => import('./icons/monitor-check.js'),
  "monitor-cog": () => import('./icons/monitor-cog.js'),
  "monitor-dot": () => import('./icons/monitor-dot.js'),
  "monitor-down": () => import('./icons/monitor-down.js'),
  "monitor-off": () => import('./icons/monitor-off.js'),
  "monitor-pause": () => import('./icons/monitor-pause.js'),
  "monitor-play": () => import('./icons/monitor-play.js'),
  "monitor-smartphone": () => import('./icons/monitor-smartphone.js'),
  "monitor-speaker": () => import('./icons/monitor-speaker.js'),
  "monitor-stop": () => import('./icons/monitor-stop.js'),
  "monitor-up": () => import('./icons/monitor-up.js'),
  "monitor-x": () => import('./icons/monitor-x.js'),
  "monitor": () => import('./icons/monitor.js'),
  "moon-star": () => import('./icons/moon-star.js'),
  "moon": () => import('./icons/moon.js'),
  "mountain-snow": () => import('./icons/mountain-snow.js'),
  "mountain": () => import('./icons/mountain.js'),
  "mouse-off": () => import('./icons/mouse-off.js'),
  "mouse-pointer-2": () => import('./icons/mouse-pointer-2.js'),
  "mouse-pointer-ban": () => import('./icons/mouse-pointer-ban.js'),
  "mouse-pointer-click": () => import('./icons/mouse-pointer-click.js'),
  "mouse-pointer": () => import('./icons/mouse-pointer.js'),
  "mouse": () => import('./icons/mouse.js'),
  "move-3d": () => import('./icons/move-3d.js'),
  "move-3-d": () => import('./icons/move-3d.js'),
  "move-diagonal-2": () => import('./icons/move-diagonal-2.js'),
  "move-diagonal": () => import('./icons/move-diagonal.js'),
  "move-down-left": () => import('./icons/move-down-left.js'),
  "move-down-right": () => import('./icons/move-down-right.js'),
  "move-down": () => import('./icons/move-down.js'),
  "move-horizontal": () => import('./icons/move-horizontal.js'),
  "move-left": () => import('./icons/move-left.js'),
  "move-right": () => import('./icons/move-right.js'),
  "move-up-left": () => import('./icons/move-up-left.js'),
  "move-up-right": () => import('./icons/move-up-right.js'),
  "move-up": () => import('./icons/move-up.js'),
  "move-vertical": () => import('./icons/move-vertical.js'),
  "move": () => import('./icons/move.js'),
  "music-2": () => import('./icons/music-2.js'),
  "music-3": () => import('./icons/music-3.js'),
  "music-4": () => import('./icons/music-4.js'),
  "music": () => import('./icons/music.js'),
  "navigation-2-off": () => import('./icons/navigation-2-off.js'),
  "navigation-2": () => import('./icons/navigation-2.js'),
  "navigation-off": () => import('./icons/navigation-off.js'),
  "navigation": () => import('./icons/navigation.js'),
  "network": () => import('./icons/network.js'),
  "newspaper": () => import('./icons/newspaper.js'),
  "nfc": () => import('./icons/nfc.js'),
  "non-binary": () => import('./icons/non-binary.js'),
  "notebook-pen": () => import('./icons/notebook-pen.js'),
  "notebook-tabs": () => import('./icons/notebook-tabs.js'),
  "notebook-text": () => import('./icons/notebook-text.js'),
  "notebook": () => import('./icons/notebook.js'),
  "notepad-text-dashed": () => import('./icons/notepad-text-dashed.js'),
  "notepad-text": () => import('./icons/notepad-text.js'),
  "nut-off": () => import('./icons/nut-off.js'),
  "nut": () => import('./icons/nut.js'),
  "octagon-alert": () => import('./icons/octagon-alert.js'),
  "alert-octagon": () => import('./icons/octagon-alert.js'),
  "octagon-minus": () => import('./icons/octagon-minus.js'),
  "octagon-pause": () => import('./icons/octagon-pause.js'),
  "pause-octagon": () => import('./icons/octagon-pause.js'),
  "octagon-x": () => import('./icons/octagon-x.js'),
  "x-octagon": () => import('./icons/octagon-x.js'),
  "octagon": () => import('./icons/octagon.js'),
  "omega": () => import('./icons/omega.js'),
  "option": () => import('./icons/option.js'),
  "orbit": () => import('./icons/orbit.js'),
  "origami": () => import('./icons/origami.js'),
  "package-2": () => import('./icons/package-2.js'),
  "package-check": () => import('./icons/package-check.js'),
  "package-minus": () => import('./icons/package-minus.js'),
  "package-open": () => import('./icons/package-open.js'),
  "package-plus": () => import('./icons/package-plus.js'),
  "package-search": () => import('./icons/package-search.js'),
  "package-x": () => import('./icons/package-x.js'),
  "package": () => import('./icons/package.js'),
  "paint-bucket": () => import('./icons/paint-bucket.js'),
  "paint-roller": () => import('./icons/paint-roller.js'),
  "paintbrush-vertical": () => import('./icons/paintbrush-vertical.js'),
  "paintbrush-2": () => import('./icons/paintbrush-vertical.js'),
  "paintbrush": () => import('./icons/paintbrush.js'),
  "palette": () => import('./icons/palette.js'),
  "panel-bottom-close": () => import('./icons/panel-bottom-close.js'),
  "panel-bottom-dashed": () => import('./icons/panel-bottom-dashed.js'),
  "panel-bottom-inactive": () => import('./icons/panel-bottom-dashed.js'),
  "panel-bottom-open": () => import('./icons/panel-bottom-open.js'),
  "panel-bottom": () => import('./icons/panel-bottom.js'),
  "panel-left-close": () => import('./icons/panel-left-close.js'),
  "sidebar-close": () => import('./icons/panel-left-close.js'),
  "panel-left-dashed": () => import('./icons/panel-left-dashed.js'),
  "panel-left-inactive": () => import('./icons/panel-left-dashed.js'),
  "panel-left-open": () => import('./icons/panel-left-open.js'),
  "sidebar-open": () => import('./icons/panel-left-open.js'),
  "panel-left": () => import('./icons/panel-left.js'),
  "sidebar": () => import('./icons/panel-left.js'),
  "panel-right-close": () => import('./icons/panel-right-close.js'),
  "panel-right-dashed": () => import('./icons/panel-right-dashed.js'),
  "panel-right-inactive": () => import('./icons/panel-right-dashed.js'),
  "panel-right-open": () => import('./icons/panel-right-open.js'),
  "panel-right": () => import('./icons/panel-right.js'),
  "panel-top-close": () => import('./icons/panel-top-close.js'),
  "panel-top-dashed": () => import('./icons/panel-top-dashed.js'),
  "panel-top-inactive": () => import('./icons/panel-top-dashed.js'),
  "panel-top-open": () => import('./icons/panel-top-open.js'),
  "panel-top": () => import('./icons/panel-top.js'),
  "panels-left-bottom": () => import('./icons/panels-left-bottom.js'),
  "panels-right-bottom": () => import('./icons/panels-right-bottom.js'),
  "panels-top-left": () => import('./icons/panels-top-left.js'),
  "layout": () => import('./icons/panels-top-left.js'),
  "paperclip": () => import('./icons/paperclip.js'),
  "parentheses": () => import('./icons/parentheses.js'),
  "parking-meter": () => import('./icons/parking-meter.js'),
  "party-popper": () => import('./icons/party-popper.js'),
  "pause": () => import('./icons/pause.js'),
  "paw-print": () => import('./icons/paw-print.js'),
  "pc-case": () => import('./icons/pc-case.js'),
  "pen-line": () => import('./icons/pen-line.js'),
  "edit-3": () => import('./icons/pen-line.js'),
  "pen-off": () => import('./icons/pen-off.js'),
  "pen-tool": () => import('./icons/pen-tool.js'),
  "pen": () => import('./icons/pen.js'),
  "edit-2": () => import('./icons/pen.js'),
  "pencil-line": () => import('./icons/pencil-line.js'),
  "pencil-off": () => import('./icons/pencil-off.js'),
  "pencil-ruler": () => import('./icons/pencil-ruler.js'),
  "pencil": () => import('./icons/pencil.js'),
  "pentagon": () => import('./icons/pentagon.js'),
  "percent": () => import('./icons/percent.js'),
  "person-standing": () => import('./icons/person-standing.js'),
  "philippine-peso": () => import('./icons/philippine-peso.js'),
  "phone-call": () => import('./icons/phone-call.js'),
  "phone-forwarded": () => import('./icons/phone-forwarded.js'),
  "phone-incoming": () => import('./icons/phone-incoming.js'),
  "phone-missed": () => import('./icons/phone-missed.js'),
  "phone-off": () => import('./icons/phone-off.js'),
  "phone-outgoing": () => import('./icons/phone-outgoing.js'),
  "phone": () => import('./icons/phone.js'),
  "pi": () => import('./icons/pi.js'),
  "piano": () => import('./icons/piano.js'),
  "pickaxe": () => import('./icons/pickaxe.js'),
  "picture-in-picture-2": () => import('./icons/picture-in-picture-2.js'),
  "picture-in-picture": () => import('./icons/picture-in-picture.js'),
  "piggy-bank": () => import('./icons/piggy-bank.js'),
  "pilcrow-left": () => import('./icons/pilcrow-left.js'),
  "pilcrow-right": () => import('./icons/pilcrow-right.js'),
  "pilcrow": () => import('./icons/pilcrow.js'),
  "pill-bottle": () => import('./icons/pill-bottle.js'),
  "pill": () => import('./icons/pill.js'),
  "pin-off": () => import('./icons/pin-off.js'),
  "pin": () => import('./icons/pin.js'),
  "pipette": () => import('./icons/pipette.js'),
  "pizza": () => import('./icons/pizza.js'),
  "plane-landing": () => import('./icons/plane-landing.js'),
  "plane-takeoff": () => import('./icons/plane-takeoff.js'),
  "plane": () => import('./icons/plane.js'),
  "play": () => import('./icons/play.js'),
  "plug-2": () => import('./icons/plug-2.js'),
  "plug-zap": () => import('./icons/plug-zap.js'),
  "plug-zap-2": () => import('./icons/plug-zap.js'),
  "plug": () => import('./icons/plug.js'),
  "plus": () => import('./icons/plus.js'),
  "pocket-knife": () => import('./icons/pocket-knife.js'),
  "pocket": () => import('./icons/pocket.js'),
  "podcast": () => import('./icons/podcast.js'),
  "pointer-off": () => import('./icons/pointer-off.js'),
  "pointer": () => import('./icons/pointer.js'),
  "popcorn": () => import('./icons/popcorn.js'),
  "popsicle": () => import('./icons/popsicle.js'),
  "pound-sterling": () => import('./icons/pound-sterling.js'),
  "power-off": () => import('./icons/power-off.js'),
  "power": () => import('./icons/power.js'),
  "presentation": () => import('./icons/presentation.js'),
  "printer-check": () => import('./icons/printer-check.js'),
  "printer": () => import('./icons/printer.js'),
  "projector": () => import('./icons/projector.js'),
  "proportions": () => import('./icons/proportions.js'),
  "puzzle": () => import('./icons/puzzle.js'),
  "pyramid": () => import('./icons/pyramid.js'),
  "qr-code": () => import('./icons/qr-code.js'),
  "quote": () => import('./icons/quote.js'),
  "rabbit": () => import('./icons/rabbit.js'),
  "radar": () => import('./icons/radar.js'),
  "radiation": () => import('./icons/radiation.js'),
  "radical": () => import('./icons/radical.js'),
  "radio-receiver": () => import('./icons/radio-receiver.js'),
  "radio-tower": () => import('./icons/radio-tower.js'),
  "radio": () => import('./icons/radio.js'),
  "radius": () => import('./icons/radius.js'),
  "rail-symbol": () => import('./icons/rail-symbol.js'),
  "rainbow": () => import('./icons/rainbow.js'),
  "rat": () => import('./icons/rat.js'),
  "ratio": () => import('./icons/ratio.js'),
  "receipt-cent": () => import('./icons/receipt-cent.js'),
  "receipt-euro": () => import('./icons/receipt-euro.js'),
  "receipt-indian-rupee": () => import('./icons/receipt-indian-rupee.js'),
  "receipt-japanese-yen": () => import('./icons/receipt-japanese-yen.js'),
  "receipt-pound-sterling": () => import('./icons/receipt-pound-sterling.js'),
  "receipt-russian-ruble": () => import('./icons/receipt-russian-ruble.js'),
  "receipt-swiss-franc": () => import('./icons/receipt-swiss-franc.js'),
  "receipt-text": () => import('./icons/receipt-text.js'),
  "receipt": () => import('./icons/receipt.js'),
  "rectangle-ellipsis": () => import('./icons/rectangle-ellipsis.js'),
  "form-input": () => import('./icons/rectangle-ellipsis.js'),
  "rectangle-horizontal": () => import('./icons/rectangle-horizontal.js'),
  "rectangle-vertical": () => import('./icons/rectangle-vertical.js'),
  "recycle": () => import('./icons/recycle.js'),
  "redo-2": () => import('./icons/redo-2.js'),
  "redo-dot": () => import('./icons/redo-dot.js'),
  "redo": () => import('./icons/redo.js'),
  "refresh-ccw-dot": () => import('./icons/refresh-ccw-dot.js'),
  "refresh-ccw": () => import('./icons/refresh-ccw.js'),
  "refresh-cw-off": () => import('./icons/refresh-cw-off.js'),
  "refresh-cw": () => import('./icons/refresh-cw.js'),
  "refrigerator": () => import('./icons/refrigerator.js'),
  "regex": () => import('./icons/regex.js'),
  "remove-formatting": () => import('./icons/remove-formatting.js'),
  "repeat-1": () => import('./icons/repeat-1.js'),
  "repeat-2": () => import('./icons/repeat-2.js'),
  "repeat": () => import('./icons/repeat.js'),
  "replace-all": () => import('./icons/replace-all.js'),
  "replace": () => import('./icons/replace.js'),
  "reply-all": () => import('./icons/reply-all.js'),
  "reply": () => import('./icons/reply.js'),
  "rewind": () => import('./icons/rewind.js'),
  "ribbon": () => import('./icons/ribbon.js'),
  "rocket": () => import('./icons/rocket.js'),
  "rocking-chair": () => import('./icons/rocking-chair.js'),
  "roller-coaster": () => import('./icons/roller-coaster.js'),
  "rotate-3d": () => import('./icons/rotate-3d.js'),
  "rotate-3-d": () => import('./icons/rotate-3d.js'),
  "rotate-ccw-square": () => import('./icons/rotate-ccw-square.js'),
  "rotate-ccw": () => import('./icons/rotate-ccw.js'),
  "rotate-cw-square": () => import('./icons/rotate-cw-square.js'),
  "rotate-cw": () => import('./icons/rotate-cw.js'),
  "route-off": () => import('./icons/route-off.js'),
  "route": () => import('./icons/route.js'),
  "router": () => import('./icons/router.js'),
  "rows-2": () => import('./icons/rows-2.js'),
  "rows": () => import('./icons/rows-2.js'),
  "rows-3": () => import('./icons/rows-3.js'),
  "panels-top-bottom": () => import('./icons/rows-3.js'),
  "rows-4": () => import('./icons/rows-4.js'),
  "rss": () => import('./icons/rss.js'),
  "ruler": () => import('./icons/ruler.js'),
  "russian-ruble": () => import('./icons/russian-ruble.js'),
  "sailboat": () => import('./icons/sailboat.js'),
  "salad": () => import('./icons/salad.js'),
  "sandwich": () => import('./icons/sandwich.js'),
  "satellite-dish": () => import('./icons/satellite-dish.js'),
  "satellite": () => import('./icons/satellite.js'),
  "saudi-riyal": () => import('./icons/saudi-riyal.js'),
  "save-all": () => import('./icons/save-all.js'),
  "save-off": () => import('./icons/save-off.js'),
  "save": () => import('./icons/save.js'),
  "scale-3d": () => import('./icons/scale-3d.js'),
  "scale-3-d": () => import('./icons/scale-3d.js'),
  "scale": () => import('./icons/scale.js'),
  "scaling": () => import('./icons/scaling.js'),
  "scan-barcode": () => import('./icons/scan-barcode.js'),
  "scan-eye": () => import('./icons/scan-eye.js'),
  "scan-face": () => import('./icons/scan-face.js'),
  "scan-heart": () => import('./icons/scan-heart.js'),
  "scan-line": () => import('./icons/scan-line.js'),
  "scan-qr-code": () => import('./icons/scan-qr-code.js'),
  "scan-search": () => import('./icons/scan-search.js'),
  "scan-text": () => import('./icons/scan-text.js'),
  "scan": () => import('./icons/scan.js'),
  "school": () => import('./icons/school.js'),
  "scissors-line-dashed": () => import('./icons/scissors-line-dashed.js'),
  "scissors": () => import('./icons/scissors.js'),
  "screen-share-off": () => import('./icons/screen-share-off.js'),
  "screen-share": () => import('./icons/screen-share.js'),
  "scroll-text": () => import('./icons/scroll-text.js'),
  "scroll": () => import('./icons/scroll.js'),
  "search-check": () => import('./icons/search-check.js'),
  "search-code": () => import('./icons/search-code.js'),
  "search-slash": () => import('./icons/search-slash.js'),
  "search-x": () => import('./icons/search-x.js'),
  "search": () => import('./icons/search.js'),
  "section": () => import('./icons/section.js'),
  "send-horizontal": () => import('./icons/send-horizontal.js'),
  "send-horizonal": () => import('./icons/send-horizontal.js'),
  "send-to-back": () => import('./icons/send-to-back.js'),
  "send": () => import('./icons/send.js'),
  "separator-horizontal": () => import('./icons/separator-horizontal.js'),
  "separator-vertical": () => import('./icons/separator-vertical.js'),
  "server-cog": () => import('./icons/server-cog.js'),
  "server-crash": () => import('./icons/server-crash.js'),
  "server-off": () => import('./icons/server-off.js'),
  "server": () => import('./icons/server.js'),
  "settings-2": () => import('./icons/settings-2.js'),
  "settings": () => import('./icons/settings.js'),
  "shapes": () => import('./icons/shapes.js'),
  "share-2": () => import('./icons/share-2.js'),
  "share": () => import('./icons/share.js'),
  "sheet": () => import('./icons/sheet.js'),
  "shell": () => import('./icons/shell.js'),
  "shield-alert": () => import('./icons/shield-alert.js'),
  "shield-ban": () => import('./icons/shield-ban.js'),
  "shield-check": () => import('./icons/shield-check.js'),
  "shield-ellipsis": () => import('./icons/shield-ellipsis.js'),
  "shield-half": () => import('./icons/shield-half.js'),
  "shield-minus": () => import('./icons/shield-minus.js'),
  "shield-off": () => import('./icons/shield-off.js'),
  "shield-plus": () => import('./icons/shield-plus.js'),
  "shield-question": () => import('./icons/shield-question.js'),
  "shield-user": () => import('./icons/shield-user.js'),
  "shield-x": () => import('./icons/shield-x.js'),
  "shield-close": () => import('./icons/shield-x.js'),
  "shield": () => import('./icons/shield.js'),
  "ship-wheel": () => import('./icons/ship-wheel.js'),
  "ship": () => import('./icons/ship.js'),
  "shirt": () => import('./icons/shirt.js'),
  "shopping-bag": () => import('./icons/shopping-bag.js'),
  "shopping-basket": () => import('./icons/shopping-basket.js'),
  "shopping-cart": () => import('./icons/shopping-cart.js'),
  "shovel": () => import('./icons/shovel.js'),
  "shower-head": () => import('./icons/shower-head.js'),
  "shrimp": () => import('./icons/shrimp.js'),
  "shrink": () => import('./icons/shrink.js'),
  "shrub": () => import('./icons/shrub.js'),
  "shuffle": () => import('./icons/shuffle.js'),
  "sigma": () => import('./icons/sigma.js'),
  "signal-high": () => import('./icons/signal-high.js'),
  "signal-low": () => import('./icons/signal-low.js'),
  "signal-medium": () => import('./icons/signal-medium.js'),
  "signal-zero": () => import('./icons/signal-zero.js'),
  "signal": () => import('./icons/signal.js'),
  "signature": () => import('./icons/signature.js'),
  "signpost-big": () => import('./icons/signpost-big.js'),
  "signpost": () => import('./icons/signpost.js'),
  "siren": () => import('./icons/siren.js'),
  "skip-back": () => import('./icons/skip-back.js'),
  "skip-forward": () => import('./icons/skip-forward.js'),
  "skull": () => import('./icons/skull.js'),
  "slack": () => import('./icons/slack.js'),
  "slash": () => import('./icons/slash.js'),
  "slice": () => import('./icons/slice.js'),
  "sliders-horizontal": () => import('./icons/sliders-horizontal.js'),
  "sliders-vertical": () => import('./icons/sliders-vertical.js'),
  "sliders": () => import('./icons/sliders-vertical.js'),
  "smartphone-charging": () => import('./icons/smartphone-charging.js'),
  "smartphone-nfc": () => import('./icons/smartphone-nfc.js'),
  "smartphone": () => import('./icons/smartphone.js'),
  "smile-plus": () => import('./icons/smile-plus.js'),
  "smile": () => import('./icons/smile.js'),
  "snail": () => import('./icons/snail.js'),
  "snowflake": () => import('./icons/snowflake.js'),
  "sofa": () => import('./icons/sofa.js'),
  "soup": () => import('./icons/soup.js'),
  "space": () => import('./icons/space.js'),
  "spade": () => import('./icons/spade.js'),
  "sparkle": () => import('./icons/sparkle.js'),
  "sparkles": () => import('./icons/sparkles.js'),
  "stars": () => import('./icons/sparkles.js'),
  "speaker": () => import('./icons/speaker.js'),
  "speech": () => import('./icons/speech.js'),
  "spell-check-2": () => import('./icons/spell-check-2.js'),
  "spell-check": () => import('./icons/spell-check.js'),
  "spline-pointer": () => import('./icons/spline-pointer.js'),
  "spline": () => import('./icons/spline.js'),
  "split": () => import('./icons/split.js'),
  "spray-can": () => import('./icons/spray-can.js'),
  "sprout": () => import('./icons/sprout.js'),
  "square-activity": () => import('./icons/square-activity.js'),
  "activity-square": () => import('./icons/square-activity.js'),
  "square-arrow-down-left": () => import('./icons/square-arrow-down-left.js'),
  "arrow-down-left-square": () => import('./icons/square-arrow-down-left.js'),
  "square-arrow-down-right": () => import('./icons/square-arrow-down-right.js'),
  "arrow-down-right-square": () => import('./icons/square-arrow-down-right.js'),
  "square-arrow-down": () => import('./icons/square-arrow-down.js'),
  "arrow-down-square": () => import('./icons/square-arrow-down.js'),
  "square-arrow-left": () => import('./icons/square-arrow-left.js'),
  "arrow-left-square": () => import('./icons/square-arrow-left.js'),
  "square-arrow-out-down-left": () => import('./icons/square-arrow-out-down-left.js'),
  "arrow-down-left-from-square": () => import('./icons/square-arrow-out-down-left.js'),
  "square-arrow-out-down-right": () => import('./icons/square-arrow-out-down-right.js'),
  "arrow-down-right-from-square": () => import('./icons/square-arrow-out-down-right.js'),
  "square-arrow-out-up-left": () => import('./icons/square-arrow-out-up-left.js'),
  "arrow-up-left-from-square": () => import('./icons/square-arrow-out-up-left.js'),
  "square-arrow-out-up-right": () => import('./icons/square-arrow-out-up-right.js'),
  "arrow-up-right-from-square": () => import('./icons/square-arrow-out-up-right.js'),
  "square-arrow-right": () => import('./icons/square-arrow-right.js'),
  "arrow-right-square": () => import('./icons/square-arrow-right.js'),
  "square-arrow-up-left": () => import('./icons/square-arrow-up-left.js'),
  "arrow-up-left-square": () => import('./icons/square-arrow-up-left.js'),
  "square-arrow-up-right": () => import('./icons/square-arrow-up-right.js'),
  "arrow-up-right-square": () => import('./icons/square-arrow-up-right.js'),
  "square-arrow-up": () => import('./icons/square-arrow-up.js'),
  "arrow-up-square": () => import('./icons/square-arrow-up.js'),
  "square-asterisk": () => import('./icons/square-asterisk.js'),
  "asterisk-square": () => import('./icons/square-asterisk.js'),
  "square-bottom-dashed-scissors": () => import('./icons/square-bottom-dashed-scissors.js'),
  "scissors-square-dashed-bottom": () => import('./icons/square-bottom-dashed-scissors.js'),
  "square-chart-gantt": () => import('./icons/square-chart-gantt.js'),
  "gantt-chart-square": () => import('./icons/square-chart-gantt.js'),
  "square-gantt-chart": () => import('./icons/square-chart-gantt.js'),
  "square-check-big": () => import('./icons/square-check-big.js'),
  "check-square": () => import('./icons/square-check-big.js'),
  "square-check": () => import('./icons/square-check.js'),
  "check-square-2": () => import('./icons/square-check.js'),
  "square-chevron-down": () => import('./icons/square-chevron-down.js'),
  "chevron-down-square": () => import('./icons/square-chevron-down.js'),
  "square-chevron-left": () => import('./icons/square-chevron-left.js'),
  "chevron-left-square": () => import('./icons/square-chevron-left.js'),
  "square-chevron-right": () => import('./icons/square-chevron-right.js'),
  "chevron-right-square": () => import('./icons/square-chevron-right.js'),
  "square-chevron-up": () => import('./icons/square-chevron-up.js'),
  "chevron-up-square": () => import('./icons/square-chevron-up.js'),
  "square-code": () => import('./icons/square-code.js'),
  "code-square": () => import('./icons/square-code.js'),
  "square-dashed-bottom-code": () => import('./icons/square-dashed-bottom-code.js'),
  "square-dashed-bottom": () => import('./icons/square-dashed-bottom.js'),
  "square-dashed-kanban": () => import('./icons/square-dashed-kanban.js'),
  "kanban-square-dashed": () => import('./icons/square-dashed-kanban.js'),
  "square-dashed-mouse-pointer": () => import('./icons/square-dashed-mouse-pointer.js'),
  "mouse-pointer-square-dashed": () => import('./icons/square-dashed-mouse-pointer.js'),
  "square-dashed": () => import('./icons/square-dashed.js'),
  "box-select": () => import('./icons/square-dashed.js'),
  "square-divide": () => import('./icons/square-divide.js'),
  "divide-square": () => import('./icons/square-divide.js'),
  "square-dot": () => import('./icons/square-dot.js'),
  "dot-square": () => import('./icons/square-dot.js'),
  "square-equal": () => import('./icons/square-equal.js'),
  "equal-square": () => import('./icons/square-equal.js'),
  "square-function": () => import('./icons/square-function.js'),
  "function-square": () => import('./icons/square-function.js'),
  "square-kanban": () => import('./icons/square-kanban.js'),
  "kanban-square": () => import('./icons/square-kanban.js'),
  "square-library": () => import('./icons/square-library.js'),
  "library-square": () => import('./icons/square-library.js'),
  "square-m": () => import('./icons/square-m.js'),
  "m-square": () => import('./icons/square-m.js'),
  "square-menu": () => import('./icons/square-menu.js'),
  "menu-square": () => import('./icons/square-menu.js'),
  "square-minus": () => import('./icons/square-minus.js'),
  "minus-square": () => import('./icons/square-minus.js'),
  "square-mouse-pointer": () => import('./icons/square-mouse-pointer.js'),
  "inspect": () => import('./icons/square-mouse-pointer.js'),
  "square-parking-off": () => import('./icons/square-parking-off.js'),
  "parking-square-off": () => import('./icons/square-parking-off.js'),
  "square-parking": () => import('./icons/square-parking.js'),
  "parking-square": () => import('./icons/square-parking.js'),
  "square-pen": () => import('./icons/square-pen.js'),
  "pen-box": () => import('./icons/square-pen.js'),
  "edit": () => import('./icons/square-pen.js'),
  "pen-square": () => import('./icons/square-pen.js'),
  "square-percent": () => import('./icons/square-percent.js'),
  "percent-square": () => import('./icons/square-percent.js'),
  "square-pi": () => import('./icons/square-pi.js'),
  "pi-square": () => import('./icons/square-pi.js'),
  "square-pilcrow": () => import('./icons/square-pilcrow.js'),
  "pilcrow-square": () => import('./icons/square-pilcrow.js'),
  "square-play": () => import('./icons/square-play.js'),
  "play-square": () => import('./icons/square-play.js'),
  "square-plus": () => import('./icons/square-plus.js'),
  "plus-square": () => import('./icons/square-plus.js'),
  "square-power": () => import('./icons/square-power.js'),
  "power-square": () => import('./icons/square-power.js'),
  "square-radical": () => import('./icons/square-radical.js'),
  "square-round-corner": () => import('./icons/square-round-corner.js'),
  "square-scissors": () => import('./icons/square-scissors.js'),
  "scissors-square": () => import('./icons/square-scissors.js'),
  "square-sigma": () => import('./icons/square-sigma.js'),
  "sigma-square": () => import('./icons/square-sigma.js'),
  "square-slash": () => import('./icons/square-slash.js'),
  "slash-square": () => import('./icons/square-slash.js'),
  "square-split-horizontal": () => import('./icons/square-split-horizontal.js'),
  "split-square-horizontal": () => import('./icons/square-split-horizontal.js'),
  "square-split-vertical": () => import('./icons/square-split-vertical.js'),
  "split-square-vertical": () => import('./icons/square-split-vertical.js'),
  "square-square": () => import('./icons/square-square.js'),
  "square-stack": () => import('./icons/square-stack.js'),
  "square-terminal": () => import('./icons/square-terminal.js'),
  "terminal-square": () => import('./icons/square-terminal.js'),
  "square-user-round": () => import('./icons/square-user-round.js'),
  "user-square-2": () => import('./icons/square-user-round.js'),
  "square-user": () => import('./icons/square-user.js'),
  "user-square": () => import('./icons/square-user.js'),
  "square-x": () => import('./icons/square-x.js'),
  "x-square": () => import('./icons/square-x.js'),
  "square": () => import('./icons/square.js'),
  "squircle": () => import('./icons/squircle.js'),
  "squirrel": () => import('./icons/squirrel.js'),
  "stamp": () => import('./icons/stamp.js'),
  "star-half": () => import('./icons/star-half.js'),
  "star-off": () => import('./icons/star-off.js'),
  "star": () => import('./icons/star.js'),
  "step-back": () => import('./icons/step-back.js'),
  "step-forward": () => import('./icons/step-forward.js'),
  "stethoscope": () => import('./icons/stethoscope.js'),
  "sticker": () => import('./icons/sticker.js'),
  "sticky-note": () => import('./icons/sticky-note.js'),
  "store": () => import('./icons/store.js'),
  "stretch-horizontal": () => import('./icons/stretch-horizontal.js'),
  "stretch-vertical": () => import('./icons/stretch-vertical.js'),
  "strikethrough": () => import('./icons/strikethrough.js'),
  "subscript": () => import('./icons/subscript.js'),
  "sun-dim": () => import('./icons/sun-dim.js'),
  "sun-medium": () => import('./icons/sun-medium.js'),
  "sun-moon": () => import('./icons/sun-moon.js'),
  "sun-snow": () => import('./icons/sun-snow.js'),
  "sun": () => import('./icons/sun.js'),
  "sunrise": () => import('./icons/sunrise.js'),
  "sunset": () => import('./icons/sunset.js'),
  "superscript": () => import('./icons/superscript.js'),
  "swatch-book": () => import('./icons/swatch-book.js'),
  "swiss-franc": () => import('./icons/swiss-franc.js'),
  "switch-camera": () => import('./icons/switch-camera.js'),
  "sword": () => import('./icons/sword.js'),
  "swords": () => import('./icons/swords.js'),
  "syringe": () => import('./icons/syringe.js'),
  "table-2": () => import('./icons/table-2.js'),
  "table-cells-merge": () => import('./icons/table-cells-merge.js'),
  "table-cells-split": () => import('./icons/table-cells-split.js'),
  "table-columns-split": () => import('./icons/table-columns-split.js'),
  "table-of-contents": () => import('./icons/table-of-contents.js'),
  "table-properties": () => import('./icons/table-properties.js'),
  "table-rows-split": () => import('./icons/table-rows-split.js'),
  "table": () => import('./icons/table.js'),
  "tablet-smartphone": () => import('./icons/tablet-smartphone.js'),
  "tablet": () => import('./icons/tablet.js'),
  "tablets": () => import('./icons/tablets.js'),
  "tag": () => import('./icons/tag.js'),
  "tags": () => import('./icons/tags.js'),
  "tally-1": () => import('./icons/tally-1.js'),
  "tally-2": () => import('./icons/tally-2.js'),
  "tally-3": () => import('./icons/tally-3.js'),
  "tally-4": () => import('./icons/tally-4.js'),
  "tally-5": () => import('./icons/tally-5.js'),
  "tangent": () => import('./icons/tangent.js'),
  "target": () => import('./icons/target.js'),
  "telescope": () => import('./icons/telescope.js'),
  "tent-tree": () => import('./icons/tent-tree.js'),
  "tent": () => import('./icons/tent.js'),
  "terminal": () => import('./icons/terminal.js'),
  "test-tube-diagonal": () => import('./icons/test-tube-diagonal.js'),
  "test-tube-2": () => import('./icons/test-tube-diagonal.js'),
  "test-tube": () => import('./icons/test-tube.js'),
  "test-tubes": () => import('./icons/test-tubes.js'),
  "text-cursor-input": () => import('./icons/text-cursor-input.js'),
  "text-cursor": () => import('./icons/text-cursor.js'),
  "text-quote": () => import('./icons/text-quote.js'),
  "text-search": () => import('./icons/text-search.js'),
  "text-select": () => import('./icons/text-select.js'),
  "text-selection": () => import('./icons/text-select.js'),
  "text": () => import('./icons/text.js'),
  "theater": () => import('./icons/theater.js'),
  "thermometer-snowflake": () => import('./icons/thermometer-snowflake.js'),
  "thermometer-sun": () => import('./icons/thermometer-sun.js'),
  "thermometer": () => import('./icons/thermometer.js'),
  "thumbs-down": () => import('./icons/thumbs-down.js'),
  "thumbs-up": () => import('./icons/thumbs-up.js'),
  "ticket-check": () => import('./icons/ticket-check.js'),
  "ticket-minus": () => import('./icons/ticket-minus.js'),
  "ticket-percent": () => import('./icons/ticket-percent.js'),
  "ticket-plus": () => import('./icons/ticket-plus.js'),
  "ticket-slash": () => import('./icons/ticket-slash.js'),
  "ticket-x": () => import('./icons/ticket-x.js'),
  "ticket": () => import('./icons/ticket.js'),
  "tickets-plane": () => import('./icons/tickets-plane.js'),
  "tickets": () => import('./icons/tickets.js'),
  "timer-off": () => import('./icons/timer-off.js'),
  "timer-reset": () => import('./icons/timer-reset.js'),
  "timer": () => import('./icons/timer.js'),
  "toggle-left": () => import('./icons/toggle-left.js'),
  "toggle-right": () => import('./icons/toggle-right.js'),
  "toilet": () => import('./icons/toilet.js'),
  "tornado": () => import('./icons/tornado.js'),
  "torus": () => import('./icons/torus.js'),
  "touchpad-off": () => import('./icons/touchpad-off.js'),
  "touchpad": () => import('./icons/touchpad.js'),
  "tower-control": () => import('./icons/tower-control.js'),
  "toy-brick": () => import('./icons/toy-brick.js'),
  "tractor": () => import('./icons/tractor.js'),
  "traffic-cone": () => import('./icons/traffic-cone.js'),
  "train-front-tunnel": () => import('./icons/train-front-tunnel.js'),
  "train-front": () => import('./icons/train-front.js'),
  "train-track": () => import('./icons/train-track.js'),
  "tram-front": () => import('./icons/tram-front.js'),
  "train": () => import('./icons/tram-front.js'),
  "transgender": () => import('./icons/transgender.js'),
  "trash-2": () => import('./icons/trash-2.js'),
  "trash": () => import('./icons/trash.js'),
  "tree-deciduous": () => import('./icons/tree-deciduous.js'),
  "tree-palm": () => import('./icons/tree-palm.js'),
  "palmtree": () => import('./icons/tree-palm.js'),
  "tree-pine": () => import('./icons/tree-pine.js'),
  "trees": () => import('./icons/trees.js'),
  "trello": () => import('./icons/trello.js'),
  "trending-down": () => import('./icons/trending-down.js'),
  "trending-up-down": () => import('./icons/trending-up-down.js'),
  "trending-up": () => import('./icons/trending-up.js'),
  "triangle-alert": () => import('./icons/triangle-alert.js'),
  "alert-triangle": () => import('./icons/triangle-alert.js'),
  "triangle-dashed": () => import('./icons/triangle-dashed.js'),
  "triangle-right": () => import('./icons/triangle-right.js'),
  "triangle": () => import('./icons/triangle.js'),
  "trophy": () => import('./icons/trophy.js'),
  "truck": () => import('./icons/truck.js'),
  "turtle": () => import('./icons/turtle.js'),
  "tv-minimal-play": () => import('./icons/tv-minimal-play.js'),
  "tv-minimal": () => import('./icons/tv-minimal.js'),
  "tv-2": () => import('./icons/tv-minimal.js'),
  "tv": () => import('./icons/tv.js'),
  "twitch": () => import('./icons/twitch.js'),
  "twitter": () => import('./icons/twitter.js'),
  "type-outline": () => import('./icons/type-outline.js'),
  "type": () => import('./icons/type.js'),
  "umbrella-off": () => import('./icons/umbrella-off.js'),
  "umbrella": () => import('./icons/umbrella.js'),
  "underline": () => import('./icons/underline.js'),
  "undo-2": () => import('./icons/undo-2.js'),
  "undo-dot": () => import('./icons/undo-dot.js'),
  "undo": () => import('./icons/undo.js'),
  "unfold-horizontal": () => import('./icons/unfold-horizontal.js'),
  "unfold-vertical": () => import('./icons/unfold-vertical.js'),
  "ungroup": () => import('./icons/ungroup.js'),
  "university": () => import('./icons/university.js'),
  "school-2": () => import('./icons/university.js'),
  "unlink-2": () => import('./icons/unlink-2.js'),
  "unlink": () => import('./icons/unlink.js'),
  "unplug": () => import('./icons/unplug.js'),
  "upload": () => import('./icons/upload.js'),
  "usb": () => import('./icons/usb.js'),
  "user-check": () => import('./icons/user-check.js'),
  "user-cog": () => import('./icons/user-cog.js'),
  "user-minus": () => import('./icons/user-minus.js'),
  "user-pen": () => import('./icons/user-pen.js'),
  "user-plus": () => import('./icons/user-plus.js'),
  "user-round-check": () => import('./icons/user-round-check.js'),
  "user-check-2": () => import('./icons/user-round-check.js'),
  "user-round-cog": () => import('./icons/user-round-cog.js'),
  "user-cog-2": () => import('./icons/user-round-cog.js'),
  "user-round-minus": () => import('./icons/user-round-minus.js'),
  "user-minus-2": () => import('./icons/user-round-minus.js'),
  "user-round-pen": () => import('./icons/user-round-pen.js'),
  "user-round-plus": () => import('./icons/user-round-plus.js'),
  "user-plus-2": () => import('./icons/user-round-plus.js'),
  "user-round-search": () => import('./icons/user-round-search.js'),
  "user-round-x": () => import('./icons/user-round-x.js'),
  "user-x-2": () => import('./icons/user-round-x.js'),
  "user-round": () => import('./icons/user-round.js'),
  "user-2": () => import('./icons/user-round.js'),
  "user-search": () => import('./icons/user-search.js'),
  "user-x": () => import('./icons/user-x.js'),
  "user": () => import('./icons/user.js'),
  "users-round": () => import('./icons/users-round.js'),
  "users-2": () => import('./icons/users-round.js'),
  "users": () => import('./icons/users.js'),
  "utensils-crossed": () => import('./icons/utensils-crossed.js'),
  "fork-knife-crossed": () => import('./icons/utensils-crossed.js'),
  "utensils": () => import('./icons/utensils.js'),
  "fork-knife": () => import('./icons/utensils.js'),
  "utility-pole": () => import('./icons/utility-pole.js'),
  "variable": () => import('./icons/variable.js'),
  "vault": () => import('./icons/vault.js'),
  "vegan": () => import('./icons/vegan.js'),
  "venetian-mask": () => import('./icons/venetian-mask.js'),
  "venus-and-mars": () => import('./icons/venus-and-mars.js'),
  "venus": () => import('./icons/venus.js'),
  "vibrate-off": () => import('./icons/vibrate-off.js'),
  "vibrate": () => import('./icons/vibrate.js'),
  "video-off": () => import('./icons/video-off.js'),
  "video": () => import('./icons/video.js'),
  "videotape": () => import('./icons/videotape.js'),
  "view": () => import('./icons/view.js'),
  "voicemail": () => import('./icons/voicemail.js'),
  "volleyball": () => import('./icons/volleyball.js'),
  "volume-1": () => import('./icons/volume-1.js'),
  "volume-2": () => import('./icons/volume-2.js'),
  "volume-off": () => import('./icons/volume-off.js'),
  "volume-x": () => import('./icons/volume-x.js'),
  "volume": () => import('./icons/volume.js'),
  "vote": () => import('./icons/vote.js'),
  "wallet-cards": () => import('./icons/wallet-cards.js'),
  "wallet-minimal": () => import('./icons/wallet-minimal.js'),
  "wallet-2": () => import('./icons/wallet-minimal.js'),
  "wallet": () => import('./icons/wallet.js'),
  "wallpaper": () => import('./icons/wallpaper.js'),
  "wand-sparkles": () => import('./icons/wand-sparkles.js'),
  "wand-2": () => import('./icons/wand-sparkles.js'),
  "wand": () => import('./icons/wand.js'),
  "warehouse": () => import('./icons/warehouse.js'),
  "washing-machine": () => import('./icons/washing-machine.js'),
  "watch": () => import('./icons/watch.js'),
  "waves-ladder": () => import('./icons/waves-ladder.js'),
  "waves": () => import('./icons/waves.js'),
  "waypoints": () => import('./icons/waypoints.js'),
  "webcam": () => import('./icons/webcam.js'),
  "webhook-off": () => import('./icons/webhook-off.js'),
  "webhook": () => import('./icons/webhook.js'),
  "weight": () => import('./icons/weight.js'),
  "wheat-off": () => import('./icons/wheat-off.js'),
  "wheat": () => import('./icons/wheat.js'),
  "whole-word": () => import('./icons/whole-word.js'),
  "wifi-high": () => import('./icons/wifi-high.js'),
  "wifi-low": () => import('./icons/wifi-low.js'),
  "wifi-off": () => import('./icons/wifi-off.js'),
  "wifi-zero": () => import('./icons/wifi-zero.js'),
  "wifi": () => import('./icons/wifi.js'),
  "wind-arrow-down": () => import('./icons/wind-arrow-down.js'),
  "wind": () => import('./icons/wind.js'),
  "wine-off": () => import('./icons/wine-off.js'),
  "wine": () => import('./icons/wine.js'),
  "workflow": () => import('./icons/workflow.js'),
  "worm": () => import('./icons/worm.js'),
  "wrap-text": () => import('./icons/wrap-text.js'),
  "wrench": () => import('./icons/wrench.js'),
  "x": () => import('./icons/x.js'),
  "youtube": () => import('./icons/youtube.js'),
  "zap-off": () => import('./icons/zap-off.js'),
  "zap": () => import('./icons/zap.js'),
  "zoom-in": () => import('./icons/zoom-in.js'),
  "zoom-out": () => import('./icons/zoom-out.js')
};

export { dynamicIconImports as default };
//# sourceMappingURL=dynamicIconImports.js.map
