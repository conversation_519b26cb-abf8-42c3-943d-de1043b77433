'use client'

import { useRouter } from 'next/navigation';
import { AppLayout } from '../components/AppLayout';
import { Dashboard } from '../components/screens/Dashboard';

export default function DashboardPage() {
  const router = useRouter();

  const handleNavigate = (path: string) => {
    router.push(path);
  };

  return (
    <AppLayout>
      <Dashboard onNavigate={handleNavigate} />
    </AppLayout>
  );
}